import{q as d,x,$ as h,A as m,y as p,v as e,z as s,M as g}from"./index-F6RtwXXX.js";import{A as u}from"./arrow-left-C9lccUgQ.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f=d("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]),j=({title:i,showBackButton:l=!1,showNotifications:r=!1,onMenuClick:t})=>{const a=x();h();const{isMobile:o}=m(),{isAuthenticated:c}=p();if(!o)return null;const n=()=>{window.history.length>1?a(-1):a(c?"/admin":"/")};return e.jsx(s.header,{initial:{y:-50,opacity:0},animate:{y:0,opacity:1},className:"fixed top-0 left-0 right-0 z-40 bg-white border-b border-gray-200 shadow-sm",children:e.jsxs("div",{className:"flex items-center justify-between px-4 py-3 safe-area-pt",children:[e.jsx("div",{className:"flex items-center space-x-3",children:l?e.jsx(s.button,{whileTap:{scale:.9},onClick:n,className:"p-2 rounded-lg hover:bg-gray-100 transition-colors",children:e.jsx(u,{size:20,className:"text-gray-700"})}):t&&e.jsx(s.button,{whileTap:{scale:.9},onClick:t,className:"p-2 rounded-lg hover:bg-gray-100 transition-colors",children:e.jsx(g,{size:20,className:"text-gray-700"})})}),e.jsx("div",{className:"flex-1 text-center",children:e.jsx("h1",{className:"text-lg font-semibold text-[#5D534B] truncate px-4",children:i})}),e.jsx("div",{className:"flex items-center space-x-2",children:r&&e.jsxs(s.button,{whileTap:{scale:.9},className:"p-2 rounded-lg hover:bg-gray-100 transition-colors relative",children:[e.jsx(f,{size:20,className:"text-gray-700"}),e.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"})]})})]})})};export{f as B,j as M};
