import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    react()
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    sourcemap: false,
    rollupOptions: {
      output: {
        // Let Vite handle chunking automatically - no manual chunks
        manualChunks: undefined
      }
    },
    chunkSizeWarningLimit: 1500, // Increase limit since we're not manually chunking
    target: 'es2020',
    minify: 'esbuild', // Use esbuild instead of terser - safer for variable mangling
  },
});
