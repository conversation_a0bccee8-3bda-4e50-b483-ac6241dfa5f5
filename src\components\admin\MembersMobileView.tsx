import React from 'react';
import { motion } from 'framer-motion';
import { Edit, Trash2, CheckCircle, XCircle, User } from 'lucide-react';
import { MemberData } from '../../context/MembersContext';
import { formatRupiah, formatDate } from '../../utils/formatters';

interface EditValues {
  name?: string;
  payment_status?: 'paid' | 'unpaid';
  payment_amount?: number;
  payment_date?: string;
}

interface MembersMobileViewProps {
  members: MemberData[];
  editingId: string | null;
  editValues: EditValues;
  toggleLoading: string | null;
  actionLoading: boolean;
  onEdit: (member: MemberData) => void;
  onDelete: (member: MemberData) => void;
  onToggleStatus: (member: MemberData) => void;
  onSaveEdit: () => void;
  onCancelEdit: () => void;
  onChange: (field: string, value: string | number) => void;
}

const MembersMobileView: React.FC<MembersMobileViewProps> = ({
  members,
  editingId,
  editValues,
  toggleLoading,
  actionLoading,
  onEdit,
  onDelete,
  onToggleStatus,
  onSaveEdit,
  onCancelEdit,
  onChange
}) => {
  if (members.length === 0) {
    return (
      <div className="text-center py-12">
        <User size={48} className="mx-auto text-gray-400 mb-4" />
        <p className="text-gray-500 text-lg">Belum ada anggota</p>
        <p className="text-gray-400 text-sm">Tambahkan anggota pertama untuk memulai</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {members.map((member, index) => (
        <motion.div
          key={member.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className="bg-white rounded-lg border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B] overflow-hidden"
        >
          {editingId === member.id ? (
            // Edit Mode
            <div className="p-4 space-y-4">
              <div>
                <label htmlFor={`name-${member.id}`} className="block text-sm font-medium text-gray-700 mb-1">
                  Nama
                </label>
                <input
                  id={`name-${member.id}`}
                  type="text"
                  value={editValues.name || ''}
                  onChange={(e) => onChange('name', e.target.value)}
                  className="w-full px-3 py-2 border-2 border-[#5D534B] rounded-md focus:outline-none focus:ring-2 focus:ring-[#B39DDB] focus:border-[#B39DDB] shadow-[2px_2px_0px_#5D534B]"
                  placeholder="Masukkan nama anggota"
                />
              </div>

              <div>
                <label htmlFor={`status-${member.id}`} className="block text-sm font-medium text-gray-700 mb-1">
                  Status Pembayaran
                </label>
                <select
                  id={`status-${member.id}`}
                  value={editValues.payment_status || ''}
                  onChange={(e) => onChange('payment_status', e.target.value)}
                  className="w-full px-3 py-2 border-2 border-[#5D534B] rounded-md focus:outline-none focus:ring-2 focus:ring-[#B39DDB] focus:border-[#B39DDB] shadow-[2px_2px_0px_#5D534B]"
                  title="Pilih status pembayaran"
                >
                  <option value="paid">Lunas</option>
                  <option value="unpaid">Belum Lunas</option>
                </select>
              </div>

              <div>
                <label htmlFor={`amount-${member.id}`} className="block text-sm font-medium text-gray-700 mb-1">
                  Jumlah Iuran
                </label>
                <input
                  id={`amount-${member.id}`}
                  type="number"
                  value={editValues.payment_amount || ''}
                  onChange={(e) => onChange('payment_amount', parseInt(e.target.value) || 0)}
                  className="w-full px-3 py-2 border-2 border-[#5D534B] rounded-md focus:outline-none focus:ring-2 focus:ring-[#B39DDB] focus:border-[#B39DDB] shadow-[2px_2px_0px_#5D534B]"
                  placeholder="Masukkan jumlah iuran"
                />
              </div>

              <div>
                <label htmlFor={`date-${member.id}`} className="block text-sm font-medium text-gray-700 mb-1">
                  Tanggal Pembayaran
                </label>
                <input
                  id={`date-${member.id}`}
                  type="date"
                  value={editValues.payment_date || ''}
                  onChange={(e) => onChange('payment_date', e.target.value)}
                  className="w-full px-3 py-2 border-2 border-[#5D534B] rounded-md focus:outline-none focus:ring-2 focus:ring-[#B39DDB] focus:border-[#B39DDB] shadow-[2px_2px_0px_#5D534B]"
                  placeholder="Pilih tanggal pembayaran"
                />
              </div>
              
              <div className="flex space-x-2 pt-2">
                <button
                  type="button"
                  onClick={() => onSaveEdit()}
                  disabled={actionLoading}
                  className="flex-1 bg-green-500 text-white px-4 py-2 rounded-md border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:bg-green-600 hover:shadow-[1px_1px_0px_#5D534B] transition-all disabled:opacity-50 text-sm font-medium"
                >
                  {actionLoading ? 'Menyimpan...' : 'Simpan'}
                </button>
                <button
                  type="button"
                  onClick={onCancelEdit}
                  className="flex-1 bg-gray-500 text-white px-4 py-2 rounded-md border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:bg-gray-600 hover:shadow-[1px_1px_0px_#5D534B] transition-all text-sm font-medium"
                >
                  Batal
                </button>
              </div>
            </div>
          ) : (
            // View Mode
            <div className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 text-lg">
                    {member.name}
                  </h3>
                  <div className="flex items-center mt-2">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-bold border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] ${
                      member.payment_status === 'paid'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {member.payment_status === 'paid' ? (
                        <CheckCircle size={14} className="mr-1" />
                      ) : (
                        <XCircle size={14} className="mr-1" />
                      )}
                      {member.payment_status === 'paid' ? 'Lunas' : 'Belum Lunas'}
                    </span>
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  <button
                    type="button"
                    onClick={() => onEdit(member)}
                    className="p-2 text-blue-600 hover:bg-blue-50 rounded-full border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[1px_1px_0px_#5D534B] transition-all bg-white"
                    aria-label={`Edit member ${member.name}`}
                    title={`Edit member ${member.name}`}
                  >
                    <Edit size={16} />
                  </button>
                  <button
                    type="button"
                    onClick={() => onDelete(member)}
                    disabled={actionLoading}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-full border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[1px_1px_0px_#5D534B] transition-all disabled:opacity-50 bg-white"
                    aria-label={`Delete member ${member.name}`}
                    title={`Delete member ${member.name}`}
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>
              
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex justify-between">
                  <span>Jumlah Iuran:</span>
                  <span className="font-medium text-gray-900">
                    {formatRupiah(member.payment_amount)}
                  </span>
                </div>
                
                {member.payment_date && (
                  <div className="flex justify-between">
                    <span>Tanggal Bayar:</span>
                    <span className="font-medium text-gray-900">
                      {formatDate(member.payment_date)}
                    </span>
                  </div>
                )}
              </div>
              
              <div className="mt-4 pt-3 border-t-2 border-[#5D534B]">
                <button
                  type="button"
                  onClick={() => onToggleStatus(member)}
                  disabled={toggleLoading === member.id}
                  className={`w-full px-4 py-3 rounded-md text-sm font-bold border-2 border-[#5D534B] shadow-[3px_3px_0px_#5D534B] hover:shadow-[1px_1px_0px_#5D534B] transition-all disabled:opacity-50 ${
                    member.payment_status === 'paid'
                      ? 'bg-red-100 text-red-800 hover:bg-red-200'
                      : 'bg-green-100 text-green-800 hover:bg-green-200'
                  }`}
                >
                  {toggleLoading === member.id
                    ? 'Memproses...'
                    : member.payment_status === 'paid'
                      ? 'Tandai Belum Lunas'
                      : 'Tandai Lunas'
                  }
                </button>
              </div>
            </div>
          )}
        </motion.div>
      ))}
    </div>
  );
};

export default MembersMobileView;
