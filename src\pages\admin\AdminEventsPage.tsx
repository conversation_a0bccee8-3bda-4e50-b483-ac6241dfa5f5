import React, { useState } from 'react';
import { Calendar, MessageSquare, Plus, Trash, Edit } from 'lucide-react';
import { Event } from '../../services/api';
import { useEventsContext } from '../../context/EventsContext';
import Modal from '../../components/Modal';
import Loader from '../../components/Loader';
import PageTitle from '../../components/PageTitle';
import { formatDate } from '../../utils/formatters';
import { toast } from 'sonner';
import { handleError } from '../../utils/errorHandler';

// Extended interface untuk form data dengan field tambahan
interface EventFormData {
  title: string;
  description: string;
  date: string;
  time: string;
  location: string;
  status?: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
  createdBy?: string;
}

// Extended Event interface dengan time property dan status yang lebih lengkap
interface ExtendedEvent extends Omit<Event, 'status'> {
  time?: string;
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
}

const AdminEventsPage = () => {
  const { events, loading, addEvent, updateEvent, deleteEvent } = useEventsContext();
  const [selectedEvent, setSelectedEvent] = useState<ExtendedEvent | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  // Form state
  const [formData, setFormData] = useState<EventFormData>({
    title: '',
    description: '',
    date: '',
    time: '',
    location: ''
  });

  // Data sudah otomatis dimuat dari EventsContext

  const resetFormData = () => {
    setFormData({
      title: '',
      description: '',
      date: '',
      time: '',
      location: ''
    });
  };

  const handleOpenModal = (event?: ExtendedEvent) => {
    if (event) {
      setSelectedEvent(event);
      setFormData({
        title: event.title,
        description: event.description || '',
        date: event.date,
        time: event.time || '',
        location: event.location
      });
      setIsEditing(true);
    } else {
      setSelectedEvent(null);
      resetFormData();
      setIsEditing(false);
    }
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedEvent(null);
    resetFormData();
    setIsEditing(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title || !formData.date || !formData.time || !formData.location) {
      toast.error('Semua field harus diisi');
      return;
    }

    try {
      if (isEditing && selectedEvent) {
        await updateEvent(selectedEvent.id, {
          ...formData,
          status: 'upcoming' as const,
          createdBy: 'admin'
        });
        toast.success('Acara berhasil diperbarui di database');
      } else {
        await addEvent({
          ...formData,
          status: 'upcoming' as const,
          createdBy: 'admin'
        });
        toast.success('Acara berhasil ditambahkan ke database');
      }
      handleCloseModal();
    } catch (error) {
      handleError(error, 'AdminEventsPage');
      toast.error('Gagal menyimpan acara ke database');
    }
  };

  const handleDeleteEvent = async (eventId: string) => {
    if (!window.confirm('Apakah Anda yakin ingin menghapus acara ini?')) return;

    try {
      setIsDeleting(true);
      await deleteEvent(eventId);
      toast.success('Acara berhasil dihapus dari database');
    } catch (error) {
      handleError(error, 'AdminEventsPage');
      toast.error('Gagal menghapus acara dari database');
    } finally {
      setIsDeleting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[50vh]">
        <Loader size="large" variant="default" text="Memuat Data Acara dari Database..." />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#F9F9F9] text-[#5D534B] p-4 sm:p-8">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 sm:mb-8">
          <PageTitle title="Kelola Agenda & Kegiatan" borderColor="border-[#FCE09B]" />
          <button
            type="button"
            onClick={() => handleOpenModal()}
            className="flex items-center px-3 py-2 sm:px-4 sm:py-2 rounded-lg bg-[#FCE09B] hover:bg-[#f9d572] text-[#5D534B] transition-colors mt-3 sm:mt-0 text-sm sm:text-base"
          >
            <Plus className="w-4 h-4 mr-2" />
            Tambah Agenda
          </button>
        </div>

        {events.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-8 rounded-lg border border-[#9DE0D2] bg-white">
            <Calendar size={64} className="text-[#9DE0D2] mb-4" />
            <p className="text-xl font-medium mb-2 text-center">Belum ada agenda</p>
            <p className="text-sm opacity-70 text-center">Tambahkan agenda atau kegiatan baru</p>
          </div>
        ) : (
          <div className="grid gap-4 sm:gap-6">
            {events.map((event) => (
              <div
                key={event.id}
                className="bg-white p-4 sm:p-6 rounded-lg border border-[#9DE0D2] shadow-sm"
              >
                <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
                  <div className="flex-grow min-w-0">
                    <h3 className="text-lg sm:text-xl font-semibold mb-1 sm:mb-2 break-words">{event.title}</h3>
                    <p className="text-sm opacity-70 mb-3 sm:mb-4 max-h-24 overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300">
                      {event.description || "Tidak ada deskripsi"}
                    </p>
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4 text-xs sm:text-sm">
                      <span className="flex items-center">
                        <Calendar className="w-4 h-4 mr-2 text-[#9DE0D2] flex-shrink-0" />
                        {formatDate(event.date)} {event.time}
                      </span>
                      <span className="flex items-center">
                        <MessageSquare className="w-4 h-4 mr-2 text-[#FCE09B] flex-shrink-0" />
                        {event.location}
                      </span>
                    </div>
                  </div>
                  <div className="flex gap-2 flex-shrink-0 self-end sm:self-start">
                    <button
                      type="button"
                      onClick={() => handleOpenModal(event as unknown as ExtendedEvent)}
                      className="p-1.5 sm:p-2 rounded-md sm:rounded-lg hover:bg-gray-100 transition-colors"
                      title="Edit"
                    >
                      <Edit className="w-4 h-4 sm:w-5 sm:h-5 text-[#9DE0D2]" />
                    </button>
                    <button
                      type="button"
                      onClick={() => handleDeleteEvent(event.id)}
                      className="p-1.5 sm:p-2 rounded-md sm:rounded-lg hover:bg-gray-100 transition-colors"
                      title="Hapus"
                      disabled={isDeleting}
                    >
                      <Trash className="w-4 h-4 sm:w-5 sm:h-5 text-[#FF9898]" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        <Modal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          title={isEditing ? "Edit Agenda/Kegiatan" : "Tambah Agenda/Kegiatan Baru"}
        >
          <form onSubmit={handleSubmit} className="space-y-4 text-[#5D534B]">
            <div>
              <label htmlFor="event-title" className="block text-sm font-medium mb-1">Judul</label>
              <input
                id="event-title"
                type="text"
                className="w-full p-2 border border-[#9DE0D2] rounded-lg"
                placeholder="Judul agenda/kegiatan"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                required
              />
            </div>
            <div>
              <label htmlFor="event-description" className="block text-sm font-medium mb-1">Deskripsi</label>
              <textarea
                id="event-description"
                className="w-full p-2 border border-[#FF9898] rounded-lg"
                rows={3}
                placeholder="Deskripsi agenda/kegiatan"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label htmlFor="event-date" className="block text-sm font-medium mb-1">Tanggal</label>
                <input 
                  id="event-date"
                  type="date" 
                  className="w-full p-2 border border-[#9DE0D2] rounded-lg"
                  value={formData.date}
                  onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                  required
                />
              </div>
              <div>
                <label htmlFor="event-time" className="block text-sm font-medium mb-1">Waktu</label>
                <input 
                  id="event-time"
                  type="time" 
                  className="w-full p-2 border border-[#FCE09B] rounded-lg"
                  value={formData.time}
                  onChange={(e) => setFormData({ ...formData, time: e.target.value })}
                  required
                />
              </div>
            </div>
            <div>
              <label htmlFor="event-location" className="block text-sm font-medium mb-1">Lokasi</label>
              <input
                id="event-location"
                type="text"
                className="w-full p-2 border border-[#9DE0D2] rounded-lg"
                placeholder="Lokasi acara"
                value={formData.location}
                onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                required
              />
            </div>
            <div className="flex flex-col sm:flex-row justify-end gap-2 sm:gap-4 pt-4">
              <button
                type="button"
                onClick={handleCloseModal}
                className="px-4 py-2 rounded-lg border border-[#FF9898] text-[#FF9898] hover:bg-[#FF9898] hover:text-white transition-colors order-last sm:order-first"
              >
                Batal
              </button>
              <button
                type="submit"
                className="px-4 py-2 rounded-lg bg-[#9DE0D2] hover:bg-[#86C9BB] text-[#5D534B] transition-colors"
              >
                {isEditing ? 'Simpan Perubahan' : 'Tambah Agenda'}
              </button>
            </div>
          </form>
        </Modal>
      </div>
    </div>
  );
};

export default AdminEventsPage; 