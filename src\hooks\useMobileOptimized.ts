import { useState, useEffect, useCallback } from 'react';

// Enhanced mobile detection with more breakpoints
const BREAKPOINTS = {
  mobile: 640,    // sm
  tablet: 768,    // md
  laptop: 1024,   // lg
  desktop: 1280   // xl
} as const;

type DeviceType = 'mobile' | 'tablet' | 'laptop' | 'desktop';
type Orientation = 'portrait' | 'landscape';

interface MobileOptimizedState {
  isMobile: boolean;
  isTablet: boolean;
  isLaptop: boolean;
  isDesktop: boolean;
  deviceType: DeviceType;
  orientation: Orientation;
  screenWidth: number;
  screenHeight: number;
  isTouch: boolean;
  isOnline: boolean;
}

export function useMobileOptimized(): MobileOptimizedState {
  const [state, setState] = useState<MobileOptimizedState>(() => {
    // Safe initial state for SSR
    if (typeof window === 'undefined') {
      return {
        isMobile: false,
        isTablet: false,
        isLaptop: false,
        isDesktop: true,
        deviceType: 'desktop',
        orientation: 'landscape',
        screenWidth: 1280,
        screenHeight: 720,
        isTouch: false,
        isOnline: true
      };
    }

    const width = window.innerWidth;
    const height = window.innerHeight;
    
    return {
      isMobile: width < BREAKPOINTS.mobile,
      isTablet: width >= BREAKPOINTS.mobile && width < BREAKPOINTS.laptop,
      isLaptop: width >= BREAKPOINTS.laptop && width < BREAKPOINTS.desktop,
      isDesktop: width >= BREAKPOINTS.desktop,
      deviceType: getDeviceType(width),
      orientation: height > width ? 'portrait' : 'landscape',
      screenWidth: width,
      screenHeight: height,
      isTouch: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
      isOnline: navigator.onLine
    };
  });

  const updateState = useCallback(() => {
    if (typeof window === 'undefined') return;

    const width = window.innerWidth;
    const height = window.innerHeight;
    
    setState({
      isMobile: width < BREAKPOINTS.mobile,
      isTablet: width >= BREAKPOINTS.mobile && width < BREAKPOINTS.laptop,
      isLaptop: width >= BREAKPOINTS.laptop && width < BREAKPOINTS.desktop,
      isDesktop: width >= BREAKPOINTS.desktop,
      deviceType: getDeviceType(width),
      orientation: height > width ? 'portrait' : 'landscape',
      screenWidth: width,
      screenHeight: height,
      isTouch: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
      isOnline: navigator.onLine
    });
  }, []);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Debounced resize handler
    let timeoutId: NodeJS.Timeout;
    const debouncedUpdate = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(updateState, 150);
    };

    // Event listeners
    window.addEventListener('resize', debouncedUpdate);
    window.addEventListener('orientationchange', debouncedUpdate);
    window.addEventListener('online', updateState);
    window.addEventListener('offline', updateState);

    // Initial update
    updateState();

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('resize', debouncedUpdate);
      window.removeEventListener('orientationchange', debouncedUpdate);
      window.removeEventListener('online', updateState);
      window.removeEventListener('offline', updateState);
    };
  }, [updateState]);

  return state;
}

function getDeviceType(width: number): DeviceType {
  if (width < BREAKPOINTS.mobile) return 'mobile';
  if (width < BREAKPOINTS.laptop) return 'tablet';
  if (width < BREAKPOINTS.desktop) return 'laptop';
  return 'desktop';
}

// Hook for responsive values
export function useResponsiveValue<T>(values: {
  mobile: T;
  tablet?: T;
  laptop?: T;
  desktop?: T;
}): T {
  const { deviceType } = useMobileOptimized();
  
  switch (deviceType) {
    case 'mobile':
      return values.mobile;
    case 'tablet':
      return values.tablet ?? values.mobile;
    case 'laptop':
      return values.laptop ?? values.tablet ?? values.mobile;
    case 'desktop':
      return values.desktop ?? values.laptop ?? values.tablet ?? values.mobile;
    default:
      return values.mobile;
  }
}

// Hook for conditional rendering based on device
export function useDeviceRender() {
  const state = useMobileOptimized();
  
  return {
    ...state,
    renderOnMobile: (component: React.ReactNode) => state.isMobile ? component : null,
    renderOnTablet: (component: React.ReactNode) => state.isTablet ? component : null,
    renderOnDesktop: (component: React.ReactNode) => (state.isLaptop || state.isDesktop) ? component : null,
    renderOnTouch: (component: React.ReactNode) => state.isTouch ? component : null
  };
}
