@echo off
echo.
echo ==========================================
echo   DANAPEMUDA - Android Studio Setup
echo ==========================================
echo.

echo Step 1: Download Android Studio
echo --------------------------------
echo Please download Android Studio from:
echo https://developer.android.com/studio
echo.
echo Install with default settings.
echo.

echo Step 2: Open DANAPEMUDA Project
echo --------------------------------
echo After Android Studio is installed, run:
echo npm run android:open
echo.

echo Step 3: Setup Virtual Device (Optional)
echo ----------------------------------------
echo In Android Studio:
echo 1. Go to Tools ^> AVD Manager
echo 2. Click "Create Virtual Device"
echo 3. Choose Pixel 4 or similar
echo 4. Download Android 11 (API 30)
echo 5. Click Finish
echo.

echo Step 4: Run the App
echo -------------------
echo In Android Studio:
echo 1. Click the "Run" button (green play icon)
echo 2. Choose your device/emulator
echo 3. Wait for app to install and launch
echo.

echo Alternative: Use Physical Device
echo ---------------------------------
echo 1. Enable Developer Options on your phone
echo 2. Enable USB Debugging
echo 3. Connect via USB
echo 4. Run the app from Android Studio
echo.

echo ==========================================
echo Ready to proceed? Press any key to open
echo Android Studio project...
echo ==========================================
pause

echo Opening Android Studio project...
npm run android:open
