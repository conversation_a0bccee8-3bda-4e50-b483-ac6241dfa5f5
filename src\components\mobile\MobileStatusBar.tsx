import { useEffect } from 'react';
import { StatusBar, Style } from '@capacitor/status-bar';
import { useMobileApp } from '../../hooks/useMobileApp';

interface MobileStatusBarProps {
  style?: 'DARK' | 'LIGHT';
  backgroundColor?: string;
}

const MobileStatusBar: React.FC<MobileStatusBarProps> = ({
  style = 'DARK',
  backgroundColor = '#5D534B'
}) => {
  const { isMobileApp, isLoading } = useMobileApp();

  useEffect(() => {
    if (isMobileApp && !isLoading) {
      const setStatusBar = async () => {
        try {
          await StatusBar.setStyle({ style: style as Style });
          await StatusBar.setBackgroundColor({ color: backgroundColor });
          
          // Show status bar if hidden
          await StatusBar.show();
        } catch (error) {
          console.error('Error setting status bar:', error);
        }
      };
      
      setStatusBar();
    }
  }, [isMobileApp, isLoading, style, backgroundColor]);

  // This component doesn't render anything
  return null;
};

export default MobileStatusBar;
