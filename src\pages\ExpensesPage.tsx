import { useEffect, useState } from 'react';
import { formatRupiah, formatDate } from '../utils/formatters';
import { Transaction } from '../services/api';
import Loader from '../components/Loader';
import { handleError } from '../utils/errorHandler';

// Extended interface for form data with additional fields
interface ExpenseFormData {
  type: 'income' | 'expense';
  description: string;
  amount: number;
  category: string;
  date: string;
  notes?: string; // Additional field for form
}

const ExpensesPage = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [newTransaction, setNewTransaction] = useState<ExpenseFormData>({
    type: 'expense',
    description: '',
    amount: 0,
    category: '',
    date: new Date().toISOString().split('T')[0],
    notes: ''
  });

  useEffect(() => {
    loadTransactions();
  }, []);

  const loadTransactions = async () => {
    try {
      setIsLoading(true);
      // TODO: Implement Firebase get transactions
      // const data = await getTransactionsFromFirebase();
      // setTransactions(data.filter((t: Transaction) => t.type === 'expense'));
      setTransactions([]); // Temporary empty array
    } catch (error) {
      handleError(error, 'ExpensesPage');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAdd = async () => {
    try {
      // TODO: Implement Firebase create transaction
      // await createTransactionInFirebase(newTransaction);
      setShowAddModal(false);
      setNewTransaction({
        type: 'expense',
        description: '',
        amount: 0,
        category: '',
        date: new Date().toISOString().split('T')[0],
        notes: ''
      });
      loadTransactions();
    } catch (error) {
      handleError(error, 'ExpensesPage');
    }
  };

  const handleDelete = async (_id: string) => {
    if (window.confirm('Apakah Anda yakin ingin menghapus pengeluaran ini?')) {
      try {
        // TODO: Implement Firebase delete transaction
        // await deleteTransactionFromFirebase(_id);
        loadTransactions();
      } catch (error) {
        handleError(error, 'ExpensesPage');
      }
    }
  };

  return (
    <div className="bg-[#F9F9F9] text-[#5D534B] rounded-2xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl md:text-3xl font-bold border-b-4 border-[#FF9898] pb-2">Pengeluaran</h1>
        <button
          type="button"
          onClick={() => setShowAddModal(true)}
          className="px-4 py-2 bg-[#FF9898] text-white rounded-lg hover:bg-[#FF7A7A] transition-colors"
        >
          Tambah Pengeluaran
        </button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Loader size="large" variant="secondary" text="Memuat Data Pengeluaran..." />
        </div>
      ) : (
        <div className="neo-card p-4 overflow-hidden animate-fade-in bg-white border-4 border-[#9DE0D2]">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b-2 border-[#5D534B]">
                  <th className="text-left py-3 px-4">Deskripsi</th>
                  <th className="text-left py-3 px-4">Tanggal</th>
                  <th className="text-left py-3 px-4">Jumlah</th>
                  <th className="text-left py-3 px-4">Kategori</th>
                  <th className="text-left py-3 px-4">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {transactions.map((transaction) => (
                  <tr key={transaction.id} className="border-b border-[#5D534B]/20">
                    <td className="py-3 px-4">{transaction.description}</td>
                    <td className="py-3 px-4">{formatDate(transaction.date)}</td>
                    <td className="py-3 px-4">{formatRupiah(transaction.amount)}</td>
                    <td className="py-3 px-4">{transaction.category || '-'}</td>
                    <td className="py-3 px-4">
                      <button
                        type="button"
                        onClick={() => handleDelete(transaction.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        Hapus
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {showAddModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg w-full max-w-md">
            <h2 className="text-xl font-bold mb-4">Tambah Pengeluaran</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="description">Deskripsi</label>
                <input
                  id="description"
                  type="text"
                  value={newTransaction.description}
                  onChange={(e) => setNewTransaction({ ...newTransaction, description: e.target.value })}
                  className="w-full p-2 border rounded"
                  placeholder="Masukkan deskripsi pengeluaran"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="amount">Jumlah</label>
                <input
                  id="amount"
                  type="number"
                  value={newTransaction.amount}
                  onChange={(e) => setNewTransaction({ ...newTransaction, amount: Number(e.target.value) })}
                  className="w-full p-2 border rounded"
                  placeholder="Masukkan jumlah pengeluaran"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="date">Tanggal</label>
                <input
                  id="date"
                  type="date"
                  value={newTransaction.date}
                  onChange={(e) => setNewTransaction({ ...newTransaction, date: e.target.value })}
                  className="w-full p-2 border rounded"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="category">Kategori</label>
                <input
                  id="category"
                  type="text"
                  value={newTransaction.category}
                  onChange={(e) => setNewTransaction({ ...newTransaction, category: e.target.value })}
                  className="w-full p-2 border rounded"
                  placeholder="Masukkan kategori pengeluaran"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="notes">Keterangan (Opsional)</label>
                <input
                  id="notes"
                  type="text"
                  value={newTransaction.notes || ''}
                  onChange={(e) => setNewTransaction({ ...newTransaction, notes: e.target.value })}
                  className="w-full p-2 border rounded"
                  placeholder="Masukkan keterangan tambahan"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="px-4 py-2 border rounded hover:bg-gray-100"
                >
                  Batal
                </button>
                <button
                  type="button"
                  onClick={handleAdd}
                  className="px-4 py-2 bg-[#FF9898] text-white rounded hover:bg-[#FF7A7A]"
                >
                  Tambah
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExpensesPage; 