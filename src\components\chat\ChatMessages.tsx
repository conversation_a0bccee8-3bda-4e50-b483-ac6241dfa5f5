import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { Download, File, MoreHorizontal } from 'lucide-react';
import { useChatContext } from '../../context/ChatContext';
import { useAuth } from '../../context/AuthContext';
import { Message } from '../../types/chat';
import MessageReactions from './MessageReactions';
import TypingIndicator from './TypingIndicator';

const ChatMessages: React.FC = () => {
  const { messages, typingUsers } = useChatContext();
  const { user } = useAuth();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [selectedMessage, setSelectedMessage] = useState<string | null>(null);

  // Auto scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('id-ID', {
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const formatDate = (date: Date) => {
    const today = new Date();
    const messageDate = new Date(date);
    
    if (messageDate.toDateString() === today.toDateString()) {
      return 'Hari ini';
    }
    
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (messageDate.toDateString() === yesterday.toDateString()) {
      return 'Kemarin';
    }
    
    return new Intl.DateTimeFormat('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }).format(messageDate);
  };

  const groupMessagesByDate = (messages: Message[]) => {
    const groups: { [key: string]: Message[] } = {};
    
    messages.forEach(message => {
      const dateKey = message.timestamp.toDateString();
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(message);
    });
    
    return groups;
  };

  const renderFileMessage = (message: Message) => {
    if (message.type === 'image') {
      return (
        <div className="max-w-sm">
          <img
            src={message.fileUrl}
            alt={message.fileName}
            className="rounded border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] max-w-full h-auto cursor-pointer hover:shadow-[4px_4px_0px_#5D534B] transition-all"
            onClick={() => window.open(message.fileUrl, '_blank')}
          />
          {message.content !== '📷 Image' && (
            <p className="mt-2 text-sm">{message.content}</p>
          )}
        </div>
      );
    }

    if (message.type === 'file') {
      return (
        <div className="flex items-center space-x-3 p-3 bg-white border-2 border-[#5D534B] rounded shadow-[2px_2px_0px_#5D534B] max-w-sm">
          <File className="w-8 h-8 text-[#5D534B]" />
          <div className="flex-1 min-w-0">
            <p className="font-bold text-[#5D534B] truncate">{message.fileName}</p>
            <p className="text-sm text-[#5D534B]/70">
              {message.fileSize ? `${(message.fileSize / 1024 / 1024).toFixed(2)} MB` : ''}
            </p>
          </div>
          <a
            href={message.fileUrl}
            download={message.fileName}
            title={`Download ${message.fileName}`}
            aria-label={`Download ${message.fileName}`}
            className="p-2 bg-[#FCE09B] border-2 border-[#5D534B] rounded hover:bg-[#FCE09B]/80 transition-colors"
          >
            <Download className="w-4 h-4 text-[#5D534B]" />
          </a>
        </div>
      );
    }

    return <p>{message.content}</p>;
  };

  const messageGroups = groupMessagesByDate(messages);

  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4">
      {Object.entries(messageGroups).map(([dateKey, dateMessages]) => (
        <div key={dateKey}>
          {/* Date Separator */}
          <div className="flex items-center justify-center my-6">
            <div className="bg-[#FCE09B] px-4 py-2 border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] rounded-full">
              <span className="text-sm font-bold text-[#5D534B]">
                {formatDate(new Date(dateKey))}
              </span>
            </div>
          </div>

          {/* Messages */}
          <div className="space-y-4">
            {dateMessages.map((message, index) => {
              const isOwnMessage = message.senderId === user?.uid;
              const showAvatar = index === 0 || dateMessages[index - 1].senderId !== message.senderId;

              return (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`flex max-w-[70%] ${isOwnMessage ? 'flex-row-reverse' : 'flex-row'}`}>
                    {/* Avatar */}
                    {!isOwnMessage && (
                      <div className="mr-3">
                        {showAvatar ? (
                          <div className="w-8 h-8 bg-[#FCE09B] border-2 border-[#5D534B] rounded-full flex items-center justify-center">
                            <span className="text-sm font-bold text-[#5D534B]">
                              {message.senderName.charAt(0).toUpperCase()}
                            </span>
                          </div>
                        ) : (
                          <div className="w-8 h-8" />
                        )}
                      </div>
                    )}

                    {/* Message Content */}
                    <div
                      className={`relative group ${
                        isOwnMessage ? 'ml-3' : ''
                      }`}
                    >
                      {/* Sender Name */}
                      {!isOwnMessage && showAvatar && (
                        <p className="text-sm font-bold text-[#5D534B] mb-1">
                          {message.senderName}
                        </p>
                      )}

                      {/* Message Bubble */}
                      <div
                        className={`p-3 border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] rounded-lg relative ${
                          isOwnMessage
                            ? 'bg-[#FCE09B] text-[#5D534B]'
                            : 'bg-white text-[#5D534B]'
                        }`}
                      >
                        {renderFileMessage(message)}

                        {/* Message Actions */}
                        <button
                          onClick={() => setSelectedMessage(
                            selectedMessage === message.id ? null : message.id
                          )}
                          title="Message options"
                          aria-label="Message options"
                          className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity p-1 bg-[#F9F9F9] border-2 border-[#5D534B] rounded-full shadow-[2px_2px_0px_#5D534B]"
                        >
                          <MoreHorizontal className="w-4 h-4 text-[#5D534B]" />
                        </button>

                        {/* Timestamp */}
                        <div className="flex items-center justify-between mt-2">
                          <span className="text-xs text-[#5D534B]/70">
                            {formatTime(message.timestamp)}
                          </span>
                          {message.isEdited && (
                            <span className="text-xs text-[#5D534B]/50 italic">
                              edited
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Reactions */}
                      <MessageReactions
                        message={message}
                        isVisible={selectedMessage === message.id}
                        onClose={() => setSelectedMessage(null)}
                      />
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      ))}

      {/* Typing Indicator */}
      <TypingIndicator users={typingUsers} />

      {/* Scroll anchor */}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default ChatMessages;
