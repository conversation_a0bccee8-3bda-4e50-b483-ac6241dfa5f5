import { ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from './ui/button';
import { ChevronLeft } from 'lucide-react';
import MobileNavigation from './mobile/MobileNavigation';
import { useMobileOptimized } from '../hooks/useMobileOptimized';

interface AdminLayoutProps {
  children: ReactNode;
}

const AdminLayout = ({ children }: AdminLayoutProps) => {
  const navigate = useNavigate();
  const { isMobile } = useMobileOptimized();

  return (
    <div className="min-h-screen bg-gray-100">
      <div className={`max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 ${isMobile ? 'pb-24' : ''}`}>
        {!isMobile && (
          <Button
            variant="ghost"
            className="mb-6 hover:bg-gray-200"
            onClick={() => navigate('/admin')}
          >
            <ChevronLeft className="w-4 h-4 mr-2" />
            Kembali ke Dashboard
          </Button>
        )}
        {children}
      </div>

      {/* Mobile Navigation */}
      <MobileNavigation />
    </div>
  );
};

export default AdminLayout;
