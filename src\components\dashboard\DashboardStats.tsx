import React from 'react';
import { motion } from 'framer-motion';
import { Users, Wallet, TrendingDown, CreditCard, Activity } from 'lucide-react';
import StatCard from '../StatCard';
import { formatRupiah } from '../../utils/formatters';

interface DashboardStats {
  totalMembers: number;
  totalEvents: number;
  totalIncome: number;
  totalExpense: number;
  totalDues: number;
  netBalance: number;
}

interface DashboardStatsProps {
  stats: DashboardStats;
  isLoading: boolean;
}

const DashboardStatsComponent: React.FC<DashboardStatsProps> = ({ stats, isLoading }) => {
  const statsConfig = [
    {
      title: "Total Anggota",
      value: stats.totalMembers.toString(),
      icon: Users,
      color: "#B39DDB",
      bgColor: "#F3E5F5"
    },
    {
      title: "Total Pemasukan",
      value: formatRupiah(stats.totalIncome),
      icon: Wallet,
      color: "#4FC3F7",
      bgColor: "#E1F5FE"
    },
    {
      title: "Total Pengeluaran",
      value: formatRupiah(stats.totalExpense),
      icon: TrendingDown,
      color: "#FF8A65",
      bgColor: "#FFF3E0"
    },
    {
      title: "Total Iuran",
      value: formatRupiah(stats.totalDues),
      icon: CreditCard,
      color: "#A1887F",
      bgColor: "#EFEBE9"
    },
    {
      title: "Saldo Bersih",
      value: formatRupiah(stats.netBalance),
      icon: Activity,
      color: stats.netBalance >= 0 ? "#66BB6A" : "#EF5350",
      bgColor: stats.netBalance >= 0 ? "#E8F5E8" : "#FFEBEE"
    }
  ];

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="bg-white p-4 sm:p-6 rounded-lg border border-gray-200 shadow-sm">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse"></div>
                <div className="h-6 bg-gray-200 rounded w-1/2 animate-pulse"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
      {statsConfig.map((stat, index) => (
        <motion.div
          key={stat.title}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className="w-full"
        >
          <StatCard
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            gradientClass={stat.bgColor}
            borderColor={stat.color}
          />
        </motion.div>
      ))}
    </div>
  );
};

export default DashboardStatsComponent;
