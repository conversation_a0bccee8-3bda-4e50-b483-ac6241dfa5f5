// Chat System Types
export interface User {
  id: string;
  name: string;
  avatar?: string;
  isOnline: boolean;
  lastSeen: Date;
  role: 'admin' | 'member';
}

export interface ChatRoom {
  id: string;
  name: string;
  type: 'general' | 'private';
  participants: string[]; // user IDs
  createdBy: string;
  createdAt: Date;
  lastMessage?: Message;
  isActive: boolean;
}

export interface Message {
  id: string;
  roomId: string;
  senderId: string;
  senderName: string;
  content: string;
  type: 'text' | 'image' | 'file';
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  timestamp: Date;
  reactions: MessageReaction[];
  isEdited: boolean;
  editedAt?: Date;
  replyTo?: string; // message ID
}

export interface MessageReaction {
  userId: string;
  emoji: string;
  timestamp: Date;
}

export interface TypingIndicator {
  userId: string;
  userName: string;
  roomId: string;
  timestamp: Date;
}

export interface ChatNotification {
  id: string;
  userId: string;
  roomId: string;
  messageId: string;
  type: 'message' | 'mention' | 'reaction';
  isRead: boolean;
  timestamp: Date;
}

// Firestore Collections Structure
export interface FirestoreSchema {
  users: {
    [userId: string]: User;
  };
  chatRooms: {
    [roomId: string]: ChatRoom;
  };
  messages: {
    [messageId: string]: Message;
  };
  typing: {
    [roomId: string]: {
      [userId: string]: TypingIndicator;
    };
  };
  notifications: {
    [userId: string]: {
      [notificationId: string]: ChatNotification;
    };
  };
}
