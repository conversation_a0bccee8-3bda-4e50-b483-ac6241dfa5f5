import{s as a,N as e}from"./index-BhULkrhh.js";const s=()=>a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-white p-4",children:a.jsxs("div",{className:"neo-card neo-gradient-pink max-w-md w-full p-8 text-center",children:[a.jsx("h1",{className:"text-8xl font-black mb-4",children:"404"}),a.jsx("p",{className:"text-2xl font-bold mb-6",children:"Halaman Tidak Ditemukan"}),a.jsx("p",{className:"mb-8",children:"<PERSON><PERSON>, halaman yang Anda cari tidak ada."}),a.jsx(e,{to:"/",className:"neo-button inline-block",children:"Kemba<PERSON> ke Beranda"})]})});export{s as default};
