# 🔧 ADB Setup Guide for Windows

## ❌ **PROBLEM:** 
`adb: The term 'adb' is not recognized`

## ✅ **SOLUTION:**

### **Option 1: Install Android Studio (Recommended)**

#### **1. Download Android Studio:**
- Go to: https://developer.android.com/studio
- Download Android Studio for Windows
- Install with default settings

#### **2. Setup SDK:**
1. Open Android Studio
2. Go to **File** > **Settings** > **Appearance & Behavior** > **System Settings** > **Android SDK**
3. Note the **Android SDK Location** (usually: `C:\Users\<USER>\AppData\Local\Android\Sdk`)
4. Install **Android SDK Platform-Tools**

#### **3. Add to PATH:**
1. Copy SDK path: `C:\Users\<USER>\AppData\Local\Android\Sdk`
2. Open **System Properties** > **Environment Variables**
3. Add to **System PATH**:
   - `C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools`
   - `C:\Users\<USER>\AppData\Local\Android\Sdk\tools`

#### **4. Restart Terminal:**
- Close and reopen PowerShell/Command Prompt
- Test: `adb version`

---

### **Option 2: Standalone ADB (Quick)**

#### **1. Download ADB:**
- Go to: https://developer.android.com/studio/releases/platform-tools
- Download **SDK Platform-Tools for Windows**
- Extract to: `C:\adb\`

#### **2. Add to PATH:**
1. Open **System Properties** > **Environment Variables**
2. Add `C:\adb\` to **System PATH**
3. Restart terminal

#### **3. Test:**
```cmd
adb version
```

---

### **Option 3: Using Chocolatey (If you have it)**

```powershell
# Install ADB via Chocolatey
choco install adb

# Test
adb version
```

---

## 🚀 **QUICK SETUP SCRIPT**

Save this as `setup-adb.bat` and run as Administrator:

```batch
@echo off
echo Setting up ADB for DANAPEMUDA...

REM Check if Android SDK exists
if exist "%LOCALAPPDATA%\Android\Sdk\platform-tools\adb.exe" (
    echo Found Android SDK, adding to PATH...
    setx PATH "%PATH%;%LOCALAPPDATA%\Android\Sdk\platform-tools" /M
    echo ADB setup complete!
) else (
    echo Android SDK not found. Please install Android Studio first.
    echo Download from: https://developer.android.com/studio
)

pause
```

---

## ✅ **VERIFICATION**

After setup, test these commands:

```cmd
# Check ADB version
adb version

# List connected devices
adb devices

# Should show something like:
# List of devices attached
# device_id    device
```

---

## 🔧 **TROUBLESHOOTING**

### **ADB still not recognized:**
1. **Restart computer** after adding to PATH
2. **Check PATH** in new terminal: `echo $env:PATH`
3. **Manual path**: Use full path to adb.exe

### **No devices found:**
1. **Enable USB Debugging** on phone
2. **Install USB drivers** for your phone
3. **Try different USB cable**
4. **Change USB mode** to File Transfer

### **Permission denied:**
1. **Allow USB debugging** on phone
2. **Revoke authorizations** in Developer Options
3. **Reconnect device**

---

## 📱 **ALTERNATIVE: Use Android Studio**

If ADB setup is complex, you can:

1. **Install Android Studio**
2. **Open project**: `npm run android:open`
3. **Use Android Studio's built-in tools**
4. **Run directly from Android Studio**

---

## 🎯 **NEXT STEPS AFTER ADB SETUP**

Once ADB is working:

```bash
# Check device connection
adb devices

# Run DANAPEMUDA mobile app
npm run android:dev
```

**Choose the option that works best for you! 🚀**
