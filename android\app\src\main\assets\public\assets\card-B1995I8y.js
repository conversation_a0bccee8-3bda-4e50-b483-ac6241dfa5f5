import{r as s,k as d,q as t}from"./index-BmrqMJCI.js";const o=s.forwardRef(({className:a,...e},r)=>d.jsx("div",{ref:r,className:t("rounded-lg border bg-card text-card-foreground shadow-sm",a),...e}));o.displayName="Card";const c=s.forwardRef(({className:a,...e},r)=>d.jsx("div",{ref:r,className:t("flex flex-col space-y-1.5 p-6",a),...e}));c.displayName="CardHeader";const i=s.forwardRef(({className:a,...e},r)=>d.jsx("h3",{ref:r,className:t("text-2xl font-semibold leading-none tracking-tight",a),...e}));i.displayName="CardTitle";const n=s.forwardRef(({className:a,...e},r)=>d.jsx("p",{ref:r,className:t("text-sm text-muted-foreground",a),...e}));n.displayName="CardDescription";const l=s.forwardRef(({className:a,...e},r)=>d.jsx("div",{ref:r,className:t("p-6 pt-0",a),...e}));l.displayName="CardContent";const m=s.forwardRef(({className:a,...e},r)=>d.jsx("div",{ref:r,className:t("flex items-center p-6 pt-0",a),...e}));m.displayName="CardFooter";export{o as C,l as a,c as b,i as c};
