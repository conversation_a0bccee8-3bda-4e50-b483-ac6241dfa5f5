// Firestore Security Rules for Chat System
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Users collection - users can read all, but only update their own
    match /users/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Chat rooms collection
    match /chatRooms/{roomId} {
      // Allow read if user is participant or it's a general room
      allow read: if request.auth != null && 
        (resource.data.type == 'general' || 
         request.auth.uid in resource.data.participants);
      
      // Allow create for authenticated users
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.createdBy;
      
      // Allow update if user is participant (for lastMessage updates)
      allow update: if request.auth != null && 
        (resource.data.type == 'general' || 
         request.auth.uid in resource.data.participants);
    }
    
    // Messages collection
    match /messages/{messageId} {
      // Allow read if user has access to the room
      allow read: if request.auth != null && 
        exists(/databases/$(database)/documents/chatRooms/$(resource.data.roomId)) &&
        (get(/databases/$(database)/documents/chatRooms/$(resource.data.roomId)).data.type == 'general' ||
         request.auth.uid in get(/databases/$(database)/documents/chatRooms/$(resource.data.roomId)).data.participants);
      
      // Allow create if user has access to the room and is the sender
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.senderId &&
        exists(/databases/$(database)/documents/chatRooms/$(request.resource.data.roomId)) &&
        (get(/databases/$(database)/documents/chatRooms/$(request.resource.data.roomId)).data.type == 'general' ||
         request.auth.uid in get(/databases/$(database)/documents/chatRooms/$(request.resource.data.roomId)).data.participants);
      
      // Allow update for reactions and edits by sender
      allow update: if request.auth != null && 
        (request.auth.uid == resource.data.senderId || 
         'reactions' in request.resource.data.diff(resource.data).affectedKeys());
    }
    
    // Typing indicators
    match /typing/{roomId}/users/{userId} {
      allow read, write: if request.auth != null && 
        exists(/databases/$(database)/documents/chatRooms/$(roomId)) &&
        (get(/databases/$(database)/documents/chatRooms/$(roomId)).data.type == 'general' ||
         request.auth.uid in get(/databases/$(database)/documents/chatRooms/$(roomId)).data.participants);
    }
    
    // Notifications
    match /notifications/{userId}/{notificationId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function hasRoomAccess(roomId) {
      let room = get(/databases/$(database)/documents/chatRooms/$(roomId));
      return room.data.type == 'general' || request.auth.uid in room.data.participants;
    }
  }
}

// Firebase Storage Rules for Chat Files
service firebase.storage {
  match /b/{bucket}/o {
    match /chat/{roomId}/{fileName} {
      // Allow read if user has access to the room
      allow read: if request.auth != null;
      
      // Allow write if user is authenticated and file size is reasonable
      allow write: if request.auth != null && 
        request.resource.size < 10 * 1024 * 1024 && // 10MB limit
        request.resource.contentType.matches('image/.*|application/pdf|text/.*|application/msword|application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    }
  }
}
