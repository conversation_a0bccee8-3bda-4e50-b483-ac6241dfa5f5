import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageSquare, Users, X, Plus } from 'lucide-react';
import { useChatContext } from '../../context/ChatContext';
import { useMobileApp } from '../../hooks/useMobileApp';
import ChatRoomList from './ChatRoomList';
import ChatMessages from './ChatMessages';
import ChatInput from './ChatInput';
import OnlineUsers from './OnlineUsers';
import { pageTransition } from '../../constants';

interface ChatContainerProps {
  isOpen: boolean;
  onClose: () => void;
}

const ChatContainer: React.FC<ChatContainerProps> = ({ isOpen, onClose }) => {
  const { currentRoom, isLoading } = useChatContext();
  const { isMobileApp } = useMobileApp();
  const [activeTab, setActiveTab] = useState<'chat' | 'users'>('chat');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [showLoadingTimeout, setShowLoadingTimeout] = useState(false);

  // Add timeout for loading state
  React.useEffect(() => {
    if (isLoading) {
      const timer = setTimeout(() => {
        setShowLoadingTimeout(true);
      }, 5000); // Show timeout message after 5 seconds

      return () => clearTimeout(timer);
    } else {
      setShowLoadingTimeout(false);
    }
  }, [isLoading]);

  // Chat only available on mobile app
  if (!isMobileApp) {
    return (
      <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
        <div className="bg-[#F9F9F9] border-4 border-[#5D534B] shadow-[8px_8px_0px_#5D534B] rounded-lg p-8 text-center max-w-md">
          <MessageSquare className="w-16 h-16 text-[#5D534B] mx-auto mb-4" />
          <h3 className="text-xl font-black text-[#5D534B] mb-2">
            Chat Hanya di Mobile App
          </h3>
          <p className="text-[#5D534B] mb-4">
            Fitur chat hanya tersedia di aplikasi mobile DANAPEMUDA.
          </p>
          <button
            type="button"
            onClick={onClose}
            className="bg-[#FCE09B] text-[#5D534B] px-6 py-3 font-bold border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B] hover:shadow-[2px_2px_0px_#5D534B] transition-all rounded"
          >
            Tutup
          </button>
        </div>
      </div>
    );
  }

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ x: '100%' }}
        animate={{ x: 0 }}
        exit={{ x: '100%' }}
        transition={{ type: 'tween', duration: 0.3 }}
        className="fixed inset-0 bg-[#F9F9F9] z-50 flex flex-col"
      >
        {/* Header */}
        <div className="bg-[#FCE09B] border-b-4 border-[#5D534B] p-4 flex items-center justify-between safe-area-pt">
          <div className="flex items-center space-x-4">
            <button
              type="button"
              onClick={onClose}
              className="p-2 bg-white border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[4px_4px_0px_#5D534B] transition-all rounded"
            >
              <X className="w-5 h-5 text-[#5D534B]" />
            </button>
            <MessageSquare className="w-6 h-6 text-[#5D534B]" />
            <h2 className="text-xl font-black text-[#5D534B]">
              {currentRoom?.name || 'DANAPEMUDA Chat'}
            </h2>
          </div>

            {/* Mobile Tab Switcher */}
            <div className="flex items-center space-x-2 md:hidden">
              <button
                onClick={() => setActiveTab('chat')}
                className={`px-3 py-1 text-sm font-bold border-2 border-[#5D534B] rounded ${
                  activeTab === 'chat'
                    ? 'bg-[#5D534B] text-white'
                    : 'bg-white text-[#5D534B]'
                }`}
              >
                Chat
              </button>
              <button
                onClick={() => setActiveTab('users')}
                className={`px-3 py-1 text-sm font-bold border-2 border-[#5D534B] rounded ${
                  activeTab === 'users'
                    ? 'bg-[#5D534B] text-white'
                    : 'bg-white text-[#5D534B]'
                }`}
              >
                Users
              </button>
            </div>

          </div>

        {/* Content */}
        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar - Rooms & Users */}
          <div className={`
            ${activeTab === 'chat' ? 'hidden md:flex' : 'flex md:flex'}
            flex-col w-full md:w-80 border-r-4 border-[#5D534B] bg-white
          `}>
              {/* Desktop Tabs */}
              <div className="hidden md:flex border-b-2 border-[#5D534B]">
                <button
                  onClick={() => setActiveTab('chat')}
                  className={`flex-1 p-3 font-bold border-r-2 border-[#5D534B] ${
                    activeTab === 'chat'
                      ? 'bg-[#FCE09B] text-[#5D534B]'
                      : 'bg-white text-[#5D534B] hover:bg-[#FCE09B]/20'
                  }`}
                >
                  <MessageSquare className="w-4 h-4 inline mr-2" />
                  Rooms
                </button>
                <button
                  onClick={() => setActiveTab('users')}
                  className={`flex-1 p-3 font-bold ${
                    activeTab === 'users'
                      ? 'bg-[#FCE09B] text-[#5D534B]'
                      : 'bg-white text-[#5D534B] hover:bg-[#FCE09B]/20'
                  }`}
                >
                  <Users className="w-4 h-4 inline mr-2" />
                  Users
                </button>
              </div>

              {/* Tab Content */}
              <div className="flex-1 overflow-hidden">
                {activeTab === 'chat' ? (
                  <ChatRoomList />
                ) : (
                  <OnlineUsers />
                )}
              </div>
            </div>

            {/* Main Chat Area */}
            <div className={`
              ${activeTab === 'chat' ? 'flex md:flex' : 'hidden md:flex'}
              flex-col flex-1
            `}>
              {isLoading ? (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin w-8 h-8 border-4 border-[#5D534B] border-t-transparent rounded-full mx-auto mb-4"></div>
                    <p className="text-[#5D534B] font-bold">Loading chat...</p>
                    {showLoadingTimeout && (
                      <div className="mt-4 p-4 bg-[#FF9898] border-2 border-[#5D534B] rounded">
                        <p className="text-[#5D534B] text-sm">
                          Taking longer than expected. Check your internet connection.
                        </p>
                        <button
                          onClick={onClose}
                          className="mt-2 bg-white text-[#5D534B] px-4 py-2 border-2 border-[#5D534B] rounded font-bold"
                        >
                          Close Chat
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              ) : currentRoom ? (
                <>
                  <ChatMessages />
                  <ChatInput />
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center p-8">
                    <MessageSquare className="w-16 h-16 text-[#5D534B] mx-auto mb-4" />
                    <h3 className="text-xl font-black text-[#5D534B] mb-2">
                      Welcome to DANAPEMUDA Chat!
                    </h3>
                    <p className="text-[#5D534B] mb-4">
                      Select a chat room or start a conversation
                    </p>
                    <button className="bg-[#FCE09B] text-[#5D534B] px-6 py-3 font-bold border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B] hover:shadow-[2px_2px_0px_#5D534B] transition-all rounded">
                      <Plus className="w-4 h-4 inline mr-2" />
                      Start Chatting
                    </button>
                  </div>
                </div>
              )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ChatContainer;
