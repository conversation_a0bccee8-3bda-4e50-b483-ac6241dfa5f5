# DANA PEMUDA - Aplikasi Manajemen Keuangan Pemuda Pemudi P<PERSON>ngan

## Tentang Aplikasi

DANA PEMUDA adalah aplikasi manajemen keuangan dan administrasi untuk organisasi Pemuda Pemudi Pesayangan. Aplikasi ini membantu pengurus dalam mengelola keuangan, angg<PERSON>, dan acara dengan mudah dan efisien.

## Fitur Utama

- **Manajemen Keuangan**: <PERSON><PERSON><PERSON>, pengel<PERSON><PERSON>, dan saldo kas Pemuda Pemudi Pesayangan
- **Manajemen Anggota**: Kelola data anggota dan status pembayaran iuran
- **Manajemen Acara**: Atur jadwal acara dan kegiatan Pemuda Pemudi Pesayangan
- **Panel Admin**: Akses khusus untuk pengurus dengan kontrol penuh terhadap data
- **WhatsApp Bot**: <PERSON><PERSON> notifikasi otomatis ke grup WhatsApp untuk transparansi keuangan

## Teknologi yang Digunakan

Proyek ini dibangun dengan:

- **Frontend**: Vite + React + TypeScript
- **Styling**: Tailwind CSS + shadcn/ui components
- **Database**: Firebase Firestore
- **Authentication**: Firebase Auth
- **Deployment**: Netlify
- **Icons**: Lucide React
- **Notifications**: WhatsApp API integration

## 🔒 Keamanan

Aplikasi ini menggunakan:
- ✅ Firebase Authentication untuk login
- ✅ Firestore Security Rules yang ketat
- ✅ Environment variables untuk credentials
- ✅ HTTPS-only deployment

Lihat [SECURITY.md](./SECURITY.md) untuk detail lengkap.

## Cara Menjalankan Proyek

Pastikan Anda memiliki Node.js & npm terinstal - [instal dengan nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Ikuti langkah-langkah berikut:

```sh
# Langkah 1: Clone repository
git clone <URL_GIT_ANDA>

# Langkah 2: Masuk ke direktori proyek
cd dana-pemuda-pesayangan

# Langkah 3: Instal dependensi yang diperlukan
npm install

# Langkah 4: Salin .env.example menjadi .env dan isi dengan credentials Firebase Anda
cp .env.example .env
# Edit .env dan isi dengan credentials Firebase yang benar

# Langkah 5: Jalankan server pengembangan
npm run dev
```

## Struktur Proyek

```
dana-pemuda-pesayangan/
├── public/              # Aset statis
├── src/                 # Kode sumber
│   ├── components/      # Komponen React
│   │   ├── ui/          # Komponen UI dari shadcn
│   ├── hooks/           # Custom hooks
│   ├── lib/             # Utilitas dan konfigurasi
│   ├── pages/           # Halaman aplikasi
│   │   ├── admin/       # Halaman admin
│   ├── services/        # Layanan dan API
│   ├── types/           # Definisi tipe TypeScript
│   └── utils/           # Fungsi utilitas
├── App.tsx              # Komponen utama
└── main.tsx            # Entry point
```

## Konfigurasi WhatsApp Bot

Untuk menggunakan fitur WhatsApp Bot, Anda perlu:

1. Menyiapkan server WhatsApp API (seperti yang didokumentasikan di file API)
2. Mengisi konfigurasi di file `.env`:
   ```
   VITE_WHATSAPP_API_URL=http://your-whatsapp-api-url
   VITE_WHATSAPP_API_KEY=your-secret-api-key
   ```
3. Mengatur WhatsApp Bot di panel admin, termasuk Session ID dan Group ID tujuan

## Kontribusi

Silakan berkontribusi dengan membuat pull request atau melaporkan issue.

## Lisensi

© 2024 Pemuda Pemudi Pesayangan. GUYUB RUKUN SALAWASE
