import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Users, Calendar, Settings, TrendingDown, Plus, LucideIcon } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

interface QuickAction {
  title: string;
  description: string;
  icon: LucideIcon;
  href: string;
  color: string;
  bgColor: string;
}

const QuickActions: React.FC = () => {
  const quickActions: QuickAction[] = [
    {
      title: "Kelola Anggota",
      description: "Tambah, edit, atau hapus data anggota",
      icon: Users,
      href: "/admin/members",
      color: "#B39DDB",
      bgColor: "#F3E5F5"
    },
    {
      title: "Kelola Acara",
      description: "Buat dan atur acara organisasi",
      icon: Calendar,
      href: "/admin/events",
      color: "#81C784",
      bgColor: "#E8F5E8"
    },
    {
      title: "<PERSON><PERSON><PERSON>",
      description: "Catat dan pantau pengeluaran",
      icon: TrendingDown,
      href: "/admin/expenses",
      color: "#FF8A65",
      bgColor: "#FFF3E0"
    },
    {
      title: "Pengaturan Iuran",
      description: "Atur nominal dan periode iuran",
      icon: Settings,
      href: "/admin/dues-settings",
      color: "#4FC3F7",
      bgColor: "#E1F5FE"
    }
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
      {quickActions.map((action, index) => (
        <motion.div
          key={action.title}
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: index * 0.1 }}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Link to={action.href} className="block h-full">
            <Card className="neo-card h-full hover:shadow-lg transition-all duration-200 cursor-pointer group">
              <CardContent className="p-4 sm:p-6 h-full flex flex-col">
                <div className="flex items-start space-x-4 flex-1">
                  <div
                    className={`flex-shrink-0 w-12 h-12 sm:w-14 sm:h-14 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200`}
                    style={{ backgroundColor: action.bgColor }}
                  >
                    <action.icon
                      size={24}
                      className="sm:w-7 sm:h-7"
                      style={{ color: action.color }}
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-base sm:text-lg font-semibold text-[#5D534B] mb-1 sm:mb-2 group-hover:text-[#B39DDB] transition-colors">
                      {action.title}
                    </h3>
                    <p className="text-xs sm:text-sm text-gray-600 leading-relaxed">
                      {action.description}
                    </p>
                  </div>
                </div>
                <div className="flex justify-end mt-3 sm:mt-4">
                  <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-gray-100 flex items-center justify-center group-hover:bg-[#B39DDB] group-hover:text-white transition-all duration-200">
                    <Plus size={14} className="sm:w-4 sm:h-4" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
        </motion.div>
      ))}
    </div>
  );
};

export default QuickActions;
