import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { LucideIcon } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { useMembersContext } from '../context/MembersContext';
import { useExpensesContext } from '../context/ExpensesContext';
import { pageTransition } from '../constants';
import PageTitle from '../components/PageTitle';
import { handleError } from '../utils/errorHandler';
import { useMobileOptimized } from '../hooks/useMobileOptimized';
import MobileHeader from '../components/mobile/MobileHeader';


// Mobile app imports (will be created)
// import { useMobileApp } from '../hooks/useMobileApp';
// import MobileStatusBar from '../components/mobile/MobileStatusBar';
// import MobileSafeArea from '../components/mobile/MobileSafeArea';

// Lazy load dashboard components for better performance
import { lazy, Suspense } from 'react';
const DashboardStats = lazy(() => import('../components/dashboard/DashboardStats'));
const RecentActivities = lazy(() => import('../components/dashboard/RecentActivities'));
const QuickActions = lazy(() => import('../components/dashboard/QuickActions'));
const DashboardExportButton = lazy(() => import('../components/dashboard/DashboardExportButton'));

// Types for dashboard export
interface DashboardMember {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  payment_status: 'paid' | 'unpaid';
  payment_amount?: number;
  payment_date?: string;
  created_at: string;
}

interface DashboardExpense {
  id: string;
  description: string;
  amount: number;
  category: string;
  date: string;
  created_at: string;
}

interface Activity {
  type: 'event' | 'member' | 'expense';
  title: string;
  date: string;
  icon: LucideIcon;
}

interface DashboardStats {
  totalMembers: number;
  totalEvents: number;
  totalIncome: number;
  totalExpense: number;
  totalDues: number;
  netBalance: number;
}

const AdminPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    totalMembers: 0,
    totalEvents: 0,
    totalIncome: 0,
    totalExpense: 0,
    totalDues: 0,
    netBalance: 0
  });
  const [recentActivities, setRecentActivities] = useState<Activity[]>([]);
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { members, loading: membersLoading } = useMembersContext();
  const { expenses, getTotalExpenses, loading: expensesLoading } = useExpensesContext();
  const { isMobile } = useMobileOptimized();

  const handleLogout = useCallback(async () => {
    try {
      await logout();
      navigate('/admin/login');
    } catch (error) {
      handleError(error, 'AdminPage');
    }
  }, [logout, navigate]);

  const loadDashboardData = useCallback(async () => {
    try {
      // Hitung total iuran dari semua anggota
      const totalDues = members.reduce((sum, member) => sum + member.payment_amount, 0);

      // Hitung total pengeluaran dari ExpensesContext
      const totalExpense = getTotalExpenses();

      setStats({
        totalMembers: members.length,
        totalEvents: 0, // TODO: Implementasi events
        totalIncome: totalDues,
        totalExpense: totalExpense,
        totalDues: totalDues,
        netBalance: totalDues - totalExpense
      });

      // Import icons dynamically to reduce bundle size
      const { Users, TrendingDown } = await import('lucide-react');

      // Aktivitas terbaru - gabungkan member dan expenses
      const recentActivitiesData: Activity[] = [
        {
          type: 'member',
          title: `${members.length} anggota terdaftar`,
          date: new Date().toISOString(),
          icon: Users
        },
        // Tambahkan 3 pengeluaran terbaru
        ...expenses.slice(-3).map(expense => ({
          type: 'expense' as const,
          title: `${expense.description} - ${expense.amount.toLocaleString('id-ID', { style: 'currency', currency: 'IDR' })}`,
          date: expense.date,
          icon: TrendingDown
        }))
      ];

      setRecentActivities(recentActivitiesData);
    } catch (error) {
      handleError(error, 'AdminPage');
    }
  }, [members, expenses, getTotalExpenses]);

  useEffect(() => {
    const loadAdminData = async () => {
      try {
        setIsLoading(true);
        if (user) {
          await loadDashboardData();
        } else {
          await handleLogout();
        }
      } catch (error) {
        handleError(error, 'AdminPage');
        await handleLogout();
      } finally {
        setIsLoading(false);
      }
    };

    loadAdminData();
  }, [user, handleLogout, loadDashboardData]);

  // Update stats ketika data members berubah
  useEffect(() => {
    if (!membersLoading) {
      loadDashboardData();
    }
  }, [members, membersLoading, loadDashboardData]);

  // Update stats ketika data expenses berubah
  useEffect(() => {
    if (!expensesLoading) {
      loadDashboardData();
    }
  }, [expenses, expensesLoading, loadDashboardData]);

  if (isLoading || membersLoading || expensesLoading) {
    return (
      <motion.div
        className="min-h-screen w-full px-4 sm:px-6 py-8 sm:py-10 bg-[#F9F9F9] text-[#5D534B]"
        initial="initial"
        animate="animate"
        exit="exit"
        variants={pageTransition}
      >
        <div className="max-w-7xl mx-auto">
          {/* Header skeleton */}
          <div className="flex flex-col sm:flex-row justify-between items-center mb-6 sm:mb-8">
            <div className="flex items-center space-x-3 mb-4 sm:mb-0">
              <div className="w-8 h-8 bg-gray-300 rounded-full animate-pulse"></div>
              <div className="w-32 h-4 bg-gray-300 rounded animate-pulse"></div>
            </div>
            <div className="w-20 h-8 bg-gray-300 rounded animate-pulse"></div>
          </div>

          {/* Stats skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 sm:mb-8">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="bg-white p-4 sm:p-6 rounded-lg border border-gray-200 shadow-sm">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse"></div>
                    <div className="h-6 bg-gray-200 rounded w-1/2 animate-pulse"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Content skeleton */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-1">
              <div className="w-32 h-6 bg-gray-300 rounded mb-4 animate-pulse"></div>
              <div className="bg-white p-4 rounded-lg border border-[#5D534B]/10 shadow-sm min-h-[200px]">
                <div className="space-y-3">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="flex items-start p-2">
                      <div className="w-8 h-8 bg-gray-300 rounded-full mr-3 animate-pulse"></div>
                      <div className="flex-1 space-y-2">
                        <div className="w-3/4 h-4 bg-gray-300 rounded animate-pulse"></div>
                        <div className="w-1/2 h-3 bg-gray-300 rounded animate-pulse"></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="lg:col-span-2">
              <div className="w-24 h-6 bg-gray-300 rounded mb-4 animate-pulse"></div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Array.from({ length: 4 }).map((_, i) => (
                  <div key={i} className="bg-white p-4 sm:p-5 rounded-lg border border-[#5D534B]/10 shadow-sm">
                    <div className="flex items-start space-x-3 sm:space-x-4">
                      <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-300 rounded-full animate-pulse"></div>
                      <div className="flex-1 space-y-2">
                        <div className="w-3/4 h-5 bg-gray-300 rounded animate-pulse"></div>
                        <div className="w-full h-4 bg-gray-300 rounded animate-pulse"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    );
  }

  if (!user) {
     return null;
  }

  return (
    <>
      {/* Mobile Header */}
      {isMobile && (
        <MobileHeader
          title="BENDAHARA"
          showNotifications={true}
        />
      )}

      <motion.div
        className={`min-h-screen w-full px-4 sm:px-6 py-8 sm:py-10 bg-[#F9F9F9] text-[#5D534B] ${
          isMobile ? 'pt-20 pb-24' : ''
        }`}
        initial="initial"
        animate="animate"
        exit="exit"
        variants={pageTransition}
      >
        <PageTitle title="BENDAHARA" />

        <div className="max-w-7xl mx-auto">
          {/* Desktop Header */}
          {!isMobile && (
            <div className="flex flex-col sm:flex-row justify-between items-center mb-6 sm:mb-8">
              <div className="flex items-center space-x-3 mb-4 sm:mb-0">
                <div className="p-2 bg-[#DDD6F3] rounded-full">
                  <div className="w-5 h-5 bg-[#B39DDB] rounded-full flex items-center justify-center">
                    <span className="text-white font-semibold text-xs">
                      {user?.email?.charAt(0).toUpperCase()}
                    </span>
                  </div>
                </div>
                <span className="font-medium text-[#5D534B] text-sm sm:text-base">{user.email}</span>
              </div>

              <div className="flex items-center space-x-3">
                <Suspense fallback={<div className="w-32 h-10 bg-gray-200 rounded animate-pulse"></div>}>
                  <DashboardExportButton
                    dashboardData={{
                      totalMembers: stats.totalMembers,
                      totalIncome: stats.totalIncome,
                      totalExpense: stats.totalExpense,
                      totalDues: stats.totalDues,
                      netBalance: stats.netBalance,
                      members: members.map(member => ({
                        ...member,
                        created_at: new Date().toISOString()
                      })) as DashboardMember[],
                      expenses: expenses.map(expense => ({
                        ...expense,
                        created_at: new Date().toISOString()
                      })) as DashboardExpense[]
                    }}
                  />
                </Suspense>
                <div className="flex space-x-2">
                  <button
                    type="button"
                    onClick={() => navigate('/')}
                    className="px-3 py-2 border border-[#FCE09B] text-[#5D534B] rounded-lg hover:bg-[#FCE09B] transition-colors text-sm flex items-center space-x-1"
                  >
                    <span>🏠</span>
                    <span className="hidden sm:inline">Beranda</span>
                  </button>
                  <button
                    type="button"
                    onClick={handleLogout}
                    className="px-4 py-2 border border-[#FF9898] text-[#5D534B] rounded-lg hover:bg-[#FF9898] transition-colors text-sm sm:text-base"
                  >
                    Logout
                  </button>
                </div>
              </div>
            </div>
          )}
        
          {/* Dashboard Stats */}
          <Suspense fallback={
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="animate-pulse">
                  <div className="bg-white p-4 sm:p-6 rounded-lg border border-gray-200 shadow-sm">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                      <div className="flex-1">
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                        <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          }>
            <DashboardStats stats={stats} isLoading={isLoading} />
          </Suspense>

          {/* Mobile Export Button */}
          {isMobile && (
            <div className="mb-6">
              <Suspense fallback={<div className="w-full h-12 bg-gray-200 rounded animate-pulse"></div>}>
                <DashboardExportButton
                  dashboardData={{
                    totalMembers: stats.totalMembers,
                    totalIncome: stats.totalIncome,
                    totalExpense: stats.totalExpense,
                    totalDues: stats.totalDues,
                    netBalance: stats.netBalance,
                    members: members.map(member => ({
                      ...member,
                      created_at: new Date().toISOString()
                    })) as DashboardMember[],
                    expenses: expenses.map(expense => ({
                      ...expense,
                      created_at: new Date().toISOString()
                    })) as DashboardExpense[]
                  }}
                  className="w-full"
                />
              </Suspense>
            </div>
          )}

          <div className={`grid gap-6 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 lg:grid-cols-3'}`}>
            {/* Recent Activities */}
            <div className={isMobile ? 'col-span-1' : 'lg:col-span-1'}>
              <Suspense fallback={
                <div className="animate-pulse">
                  <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
                  <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm min-h-[200px]">
                    <div className="space-y-3">
                      {Array.from({ length: 4 }).map((_, index) => (
                        <div key={index} className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                          <div className="flex-1">
                            <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
                            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              }>
                <RecentActivities activities={recentActivities} isLoading={isLoading} />
              </Suspense>
            </div>

            {/* Quick Actions - Only show on desktop */}
            {!isMobile && (
              <div className="lg:col-span-2">
                <h3 className="text-base sm:text-lg font-bold mb-3 sm:mb-4 text-[#5D534B]">
                  Menu Utama
                </h3>
                <Suspense fallback={
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {Array.from({ length: 4 }).map((_, index) => (
                      <div key={index} className="animate-pulse">
                        <div className="bg-white p-4 sm:p-6 rounded-lg border border-gray-200 shadow-sm">
                          <div className="flex items-start space-x-4">
                            <div className="w-12 h-12 bg-gray-200 rounded-xl"></div>
                            <div className="flex-1">
                              <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
                              <div className="h-4 bg-gray-200 rounded w-full"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                }>
                  <QuickActions />
                </Suspense>
              </div>
            )}
          </div>
        </div>
      </motion.div>

    </>
  );
};

export default AdminPage;