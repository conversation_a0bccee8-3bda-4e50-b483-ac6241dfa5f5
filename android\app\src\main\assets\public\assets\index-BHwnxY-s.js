const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/web-BUn-cP9F.js","assets/index-WP5euzF_.js","assets/index-DBzkvmoo.css"])))=>i.map(i=>d[i]);
import{Q as t,_ as n}from"./index-WP5euzF_.js";var e;(function(r){r.Heavy="HEAVY",r.Medium="MEDIUM",r.Light="LIGHT"})(e||(e={}));var i;(function(r){r.Success="SUCCESS",r.Warning="WARNING",r.Error="ERROR"})(i||(i={}));const a=t("Haptics",{web:()=>n(()=>import("./web-BUn-cP9F.js"),__vite__mapDeps([0,1,2])).then(r=>new r.HapticsWeb)});export{a as Haptics,e as ImpactStyle,i as NotificationType};
