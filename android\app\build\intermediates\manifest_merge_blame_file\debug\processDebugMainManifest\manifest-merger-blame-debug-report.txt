1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.danapemuda.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12
13    <uses-permission android:name="android.permission.INTERNET" />
13-->D:\PMD\android\app\src\main\AndroidManifest.xml:40:5-67
13-->D:\PMD\android\app\src\main\AndroidManifest.xml:40:22-64
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->[:capacitor-haptics] D:\PMD\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
14-->[:capacitor-haptics] D:\PMD\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->[:capacitor-network] D:\PMD\node_modules\@capacitor\network\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
15-->[:capacitor-network] D:\PMD\node_modules\@capacitor\network\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
16
17    <permission
17-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\0532590b7a4aab564854b7a52547b35e\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
18        android:name="com.danapemuda.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
18-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\0532590b7a4aab564854b7a52547b35e\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
19        android:protectionLevel="signature" />
19-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\0532590b7a4aab564854b7a52547b35e\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
20
21    <uses-permission android:name="com.danapemuda.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
21-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\0532590b7a4aab564854b7a52547b35e\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
21-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\0532590b7a4aab564854b7a52547b35e\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
22
23    <application
23-->D:\PMD\android\app\src\main\AndroidManifest.xml:4:5-36:19
24        android:allowBackup="true"
24-->D:\PMD\android\app\src\main\AndroidManifest.xml:5:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\0532590b7a4aab564854b7a52547b35e\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:icon="@mipmap/ic_launcher"
28-->D:\PMD\android\app\src\main\AndroidManifest.xml:6:9-43
29        android:label="@string/app_name"
29-->D:\PMD\android\app\src\main\AndroidManifest.xml:7:9-41
30        android:roundIcon="@mipmap/ic_launcher_round"
30-->D:\PMD\android\app\src\main\AndroidManifest.xml:8:9-54
31        android:supportsRtl="true"
31-->D:\PMD\android\app\src\main\AndroidManifest.xml:9:9-35
32        android:testOnly="true"
33        android:theme="@style/AppTheme" >
33-->D:\PMD\android\app\src\main\AndroidManifest.xml:10:9-40
34        <activity
34-->D:\PMD\android\app\src\main\AndroidManifest.xml:12:9-25:20
35            android:name="com.danapemuda.app.MainActivity"
35-->D:\PMD\android\app\src\main\AndroidManifest.xml:14:13-41
36            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
36-->D:\PMD\android\app\src\main\AndroidManifest.xml:13:13-140
37            android:exported="true"
37-->D:\PMD\android\app\src\main\AndroidManifest.xml:18:13-36
38            android:label="@string/title_activity_main"
38-->D:\PMD\android\app\src\main\AndroidManifest.xml:15:13-56
39            android:launchMode="singleTask"
39-->D:\PMD\android\app\src\main\AndroidManifest.xml:17:13-44
40            android:theme="@style/AppTheme.NoActionBarLaunch" >
40-->D:\PMD\android\app\src\main\AndroidManifest.xml:16:13-62
41            <intent-filter>
41-->D:\PMD\android\app\src\main\AndroidManifest.xml:20:13-23:29
42                <action android:name="android.intent.action.MAIN" />
42-->D:\PMD\android\app\src\main\AndroidManifest.xml:21:17-69
42-->D:\PMD\android\app\src\main\AndroidManifest.xml:21:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->D:\PMD\android\app\src\main\AndroidManifest.xml:22:17-77
44-->D:\PMD\android\app\src\main\AndroidManifest.xml:22:27-74
45            </intent-filter>
46        </activity>
47
48        <provider
49            android:name="androidx.core.content.FileProvider"
49-->D:\PMD\android\app\src\main\AndroidManifest.xml:28:13-62
50            android:authorities="com.danapemuda.app.fileprovider"
50-->D:\PMD\android\app\src\main\AndroidManifest.xml:29:13-64
51            android:exported="false"
51-->D:\PMD\android\app\src\main\AndroidManifest.xml:30:13-37
52            android:grantUriPermissions="true" >
52-->D:\PMD\android\app\src\main\AndroidManifest.xml:31:13-47
53            <meta-data
53-->D:\PMD\android\app\src\main\AndroidManifest.xml:32:13-34:64
54                android:name="android.support.FILE_PROVIDER_PATHS"
54-->D:\PMD\android\app\src\main\AndroidManifest.xml:33:17-67
55                android:resource="@xml/file_paths" />
55-->D:\PMD\android\app\src\main\AndroidManifest.xml:34:17-51
56        </provider>
57        <provider
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ee314b8ac8ef8e018671188902e26743\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
58            android:name="androidx.startup.InitializationProvider"
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ee314b8ac8ef8e018671188902e26743\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
59            android:authorities="com.danapemuda.app.androidx-startup"
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ee314b8ac8ef8e018671188902e26743\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
60            android:exported="false" >
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ee314b8ac8ef8e018671188902e26743\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
61            <meta-data
61-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ee314b8ac8ef8e018671188902e26743\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.emoji2.text.EmojiCompatInitializer"
62-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ee314b8ac8ef8e018671188902e26743\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
63                android:value="androidx.startup" />
63-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ee314b8ac8ef8e018671188902e26743\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
64            <meta-data
64-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\d3aaa5e1ecdbd6908da0b8ab0b99cd3c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
65                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
65-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\d3aaa5e1ecdbd6908da0b8ab0b99cd3c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
66                android:value="androidx.startup" />
66-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\d3aaa5e1ecdbd6908da0b8ab0b99cd3c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
67            <meta-data
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
68                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
69                android:value="androidx.startup" />
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
70        </provider>
71
72        <receiver
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
73            android:name="androidx.profileinstaller.ProfileInstallReceiver"
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
74            android:directBootAware="false"
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
75            android:enabled="true"
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
76            android:exported="true"
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
77            android:permission="android.permission.DUMP" >
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
79                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
80            </intent-filter>
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
82                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
83            </intent-filter>
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
85                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
86            </intent-filter>
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
88                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
89            </intent-filter>
90        </receiver>
91    </application>
92
93</manifest>
