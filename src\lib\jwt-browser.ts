/**
 * Implementasi sederhana JWT untuk browser
 * Ini adalah alternatif untuk jsonwebtoken yang hanya berjalan di Node.js
 */

// Fungsi untuk encode base64url
function base64UrlEncode(str: string): string {
  return btoa(str)
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '');
}

// Fungsi untuk decode base64url
function base64UrlDecode(str: string): string {
  str = str.replace(/-/g, '+').replace(/_/g, '/');
  while (str.length % 4) {
    str += '=';
  }
  return atob(str);
}

// Interface untuk JWT payload
interface JWTPayload {
  [key: string]: unknown;
  exp?: number;
}

// Fungsi untuk menghasilkan token JWT
export function sign(payload: JWTPayload, secret: string, options: { expiresIn?: string } = {}): string {
  // Header default
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };

  // Tambahkan expiration jika ada
  if (options.expiresIn) {
    const now = Math.floor(Date.now() / 1000);
    let expiresIn = 0;
    
    if (options.expiresIn.endsWith('d')) {
      const days = parseInt(options.expiresIn.slice(0, -1));
      expiresIn = days * 24 * 60 * 60;
    } else if (options.expiresIn.endsWith('h')) {
      const hours = parseInt(options.expiresIn.slice(0, -1));
      expiresIn = hours * 60 * 60;
    } else if (options.expiresIn.endsWith('m')) {
      const minutes = parseInt(options.expiresIn.slice(0, -1));
      expiresIn = minutes * 60;
    } else if (options.expiresIn.endsWith('s')) {
      expiresIn = parseInt(options.expiresIn.slice(0, -1));
    } else {
      expiresIn = parseInt(options.expiresIn);
    }
    
    payload.exp = now + expiresIn;
  }

  // Encode header dan payload
  const encodedHeader = base64UrlEncode(JSON.stringify(header));
  const encodedPayload = base64UrlEncode(JSON.stringify(payload));
  
  // Buat signature (ini hanya simulasi, tidak benar-benar aman)
  // Di implementasi nyata, gunakan SubtleCrypto untuk HMAC
  const data = encodedHeader + '.' + encodedPayload;
  const signature = base64UrlEncode(
    Array.from(new TextEncoder().encode(data + secret))
      .map(b => String.fromCharCode(b))
      .join('')
  );
  
  // Gabungkan semua bagian
  return `${encodedHeader}.${encodedPayload}.${signature}`;
}

// Fungsi untuk memverifikasi token JWT
export function verify(token: string, _secret: string): JWTPayload {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid token format');
    }

    const [, encodedPayload] = parts;

    // Decode payload
    const payload = JSON.parse(base64UrlDecode(encodedPayload)) as JWTPayload;

    // Periksa expiration
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      throw new Error('Token expired');
    }

    // Di implementasi nyata, verifikasi signature dengan SubtleCrypto
    // Untuk sementara, kita asumsikan token valid
    // Note: secret parameter diawali dengan _ untuk menunjukkan tidak digunakan

    return payload;
  } catch (error) {
    throw new Error('Invalid token');
  }
}

export default {
  sign,
  verify
};
