import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>L<PERSON>t, <PERSON><PERSON>, Bell } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useMobileOptimized } from '../../hooks/useMobileOptimized';
import { useAuth } from '../../context/AuthContext';

interface MobileHeaderProps {
  title: string;
  showBackButton?: boolean;
  showNotifications?: boolean;
  onMenuClick?: () => void;
}

const MobileHeader: React.FC<MobileHeaderProps> = ({
  title,
  showBackButton = false,
  showNotifications = false,
  onMenuClick
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isMobile } = useMobileOptimized();
  const { isAuthenticated } = useAuth();

  // Don't show on desktop
  if (!isMobile) {
    return null;
  }

  const handleBack = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      // Fallback navigation
      if (isAuthenticated) {
        navigate('/admin');
      } else {
        navigate('/');
      }
    }
  };

  return (
    <motion.header
      initial={{ y: -50, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className="fixed top-0 left-0 right-0 z-40 bg-white border-b border-gray-200 shadow-sm"
    >
      <div className="flex items-center justify-between px-4 py-3 safe-area-pt">
        {/* Left side */}
        <div className="flex items-center space-x-3">
          {showBackButton ? (
            <motion.button
              whileTap={{ scale: 0.9 }}
              onClick={handleBack}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <ArrowLeft size={20} className="text-gray-700" />
            </motion.button>
          ) : (
            onMenuClick && (
              <motion.button
                whileTap={{ scale: 0.9 }}
                onClick={onMenuClick}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <Menu size={20} className="text-gray-700" />
              </motion.button>
            )
          )}
        </div>

        {/* Center - Title */}
        <div className="flex-1 text-center">
          <h1 className="text-lg font-semibold text-[#5D534B] truncate px-4">
            {title}
          </h1>
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-2">
          {showNotifications && (
            <motion.button
              whileTap={{ scale: 0.9 }}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors relative"
            >
              <Bell size={20} className="text-gray-700" />
              {/* Notification badge */}
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
            </motion.button>
          )}
        </div>
      </div>
    </motion.header>
  );
};

export default MobileHeader;
