import { Capacitor } from '@capacitor/core';
import { Preferences } from '@capacitor/preferences';

export interface ChatUser {
  id: string;
  name: string;
  avatar?: string;
  createdAt: Date;
  lastActive: Date;
}

export class UserStorageService {
  private static readonly USER_KEY = 'danapemuda_chat_user';

  // Save user to local storage
  static async saveUser(user: ChatUser): Promise<void> {
    try {
      const userData = {
        ...user,
        createdAt: user.createdAt.toISOString(),
        lastActive: user.lastActive.toISOString()
      };

      if (Capacitor.isNativePlatform()) {
        await Preferences.set({
          key: this.USER_KEY,
          value: JSON.stringify(userData)
        });
      } else {
        localStorage.setItem(this.USER_KEY, JSON.stringify(userData));
      }
    } catch (error) {
      console.error('Error saving user:', error);
    }
  }

  // Get user from local storage
  static async getUser(): Promise<ChatUser | null> {
    try {
      let userData: string | null = null;

      if (Capacitor.isNativePlatform()) {
        const result = await Preferences.get({ key: this.USER_KEY });
        userData = result.value;
      } else {
        userData = localStorage.getItem(this.USER_KEY);
      }

      if (!userData) return null;

      const parsed = JSON.parse(userData);
      return {
        ...parsed,
        createdAt: new Date(parsed.createdAt),
        lastActive: new Date(parsed.lastActive)
      };
    } catch (error) {
      console.error('Error getting user:', error);
      return null;
    }
  }

  // Update last active time
  static async updateLastActive(userId: string): Promise<void> {
    try {
      const user = await this.getUser();
      if (user && user.id === userId) {
        user.lastActive = new Date();
        await this.saveUser(user);
      }
    } catch (error) {
      console.error('Error updating last active:', error);
    }
  }

  // Clear user data (for logout/reset)
  static async clearUser(): Promise<void> {
    try {
      if (Capacitor.isNativePlatform()) {
        await Preferences.remove({ key: this.USER_KEY });
      } else {
        localStorage.removeItem(this.USER_KEY);
      }
    } catch (error) {
      console.error('Error clearing user:', error);
    }
  }

  // Generate unique user ID
  static generateUserId(): string {
    return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // Generate random avatar color
  static generateAvatarColor(): string {
    const colors = [
      '#B39DDB', '#81C784', '#FF8A65', '#4FC3F7', 
      '#FFB74D', '#F06292', '#A1C181', '#FFD54F'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }
}
