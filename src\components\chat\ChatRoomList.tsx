import React from 'react';
import { motion } from 'framer-motion';
import { MessageSquare, Lock, Hash, Users } from 'lucide-react';
import { useChatContext } from '../../context/ChatContext';
import { ChatRoom } from '../../types/chat';

const ChatRoomList: React.FC = () => {
  const { rooms, currentRoom, setCurrentRoom } = useChatContext();

  const formatLastMessage = (room: ChatRoom) => {
    if (!room.lastMessage) return 'No messages yet';
    
    const content = room.lastMessage.content;
    if (content.startsWith('📷')) return '📷 Image';
    if (content.startsWith('📎')) return '📎 File';
    
    return content.length > 30 ? `${content.substring(0, 30)}...` : content;
  };

  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'now';
    if (minutes < 60) return `${minutes}m`;
    if (hours < 24) return `${hours}h`;
    if (days < 7) return `${days}d`;
    
    return new Intl.DateTimeFormat('id-ID', {
      day: 'numeric',
      month: 'short'
    }).format(date);
  };

  const getRoomIcon = (room: ChatRoom) => {
    if (room.type === 'general') return Hash;
    if (room.type === 'private') return Lock;
    return MessageSquare;
  };

  const getRoomParticipantCount = (room: ChatRoom) => {
    if (room.type === 'general') return 'All members';
    if (room.type === 'private') return '2 members';
    return `${room.participants.length} members`;
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b-2 border-[#5D534B] bg-[#FCE09B]">
        <h3 className="font-black text-[#5D534B] flex items-center">
          <MessageSquare className="w-5 h-5 mr-2" />
          Chat Rooms
        </h3>
      </div>

      {/* Room List */}
      <div className="flex-1 overflow-y-auto">
        {rooms.length === 0 ? (
          <div className="p-4 text-center">
            <MessageSquare className="w-12 h-12 text-[#5D534B]/50 mx-auto mb-3" />
            <p className="text-[#5D534B]/70 font-medium">No chat rooms yet</p>
            <p className="text-sm text-[#5D534B]/50">Start a conversation!</p>
          </div>
        ) : (
          <div className="p-2 space-y-2">
            {rooms.map((room) => {
              const Icon = getRoomIcon(room);
              const isActive = currentRoom?.id === room.id;
              
              return (
                <motion.button
                  key={room.id}
                  onClick={() => setCurrentRoom(room)}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={`w-full p-3 text-left border-2 border-[#5D534B] rounded transition-all ${
                    isActive
                      ? 'bg-[#FCE09B] shadow-[4px_4px_0px_#5D534B]'
                      : 'bg-white shadow-[2px_2px_0px_#5D534B] hover:shadow-[4px_4px_0px_#5D534B]'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    {/* Room Icon */}
                    <div className={`p-2 rounded border-2 border-[#5D534B] ${
                      room.type === 'general' 
                        ? 'bg-[#9DE0D2]' 
                        : room.type === 'private'
                        ? 'bg-[#FF9898]'
                        : 'bg-[#FCE09B]'
                    }`}>
                      <Icon className="w-4 h-4 text-[#5D534B]" />
                    </div>

                    {/* Room Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="font-bold text-[#5D534B] truncate">
                          {room.name}
                        </h4>
                        {room.lastMessage && (
                          <span className="text-xs text-[#5D534B]/70 ml-2">
                            {formatTime(new Date(room.lastMessage.timestamp))}
                          </span>
                        )}
                      </div>
                      
                      <div className="flex items-center text-xs text-[#5D534B]/70 mb-1">
                        <Users className="w-3 h-3 mr-1" />
                        {getRoomParticipantCount(room)}
                      </div>
                      
                      {room.lastMessage && (
                        <p className="text-sm text-[#5D534B]/80 truncate">
                          <span className="font-medium">
                            {room.lastMessage.senderId === 'current-user' ? 'You' : room.lastMessage.senderName}:
                          </span>{' '}
                          {formatLastMessage(room)}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Unread Indicator */}
                  {/* TODO: Implement unread count */}
                  {false && (
                    <div className="absolute top-2 right-2 w-2 h-2 bg-[#FF9898] border border-[#5D534B] rounded-full"></div>
                  )}
                </motion.button>
              );
            })}
          </div>
        )}
      </div>

      {/* Create Room Button */}
      <div className="p-4 border-t-2 border-[#5D534B]">
        <button className="w-full p-3 bg-[#FCE09B] text-[#5D534B] font-bold border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[4px_4px_0px_#5D534B] transition-all rounded">
          <MessageSquare className="w-4 h-4 inline mr-2" />
          Create Room
        </button>
      </div>
    </div>
  );
};

export default ChatRoomList;
