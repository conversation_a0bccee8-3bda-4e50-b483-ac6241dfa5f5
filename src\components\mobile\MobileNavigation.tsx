import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Home, Users, Calendar, Wallet, Settings, LogOut, TrendingDown, MessageSquare, LucideIcon } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { useMobileOptimized } from '../../hooks/useMobileOptimized';
import { SimpleChatProvider } from '../../context/SimpleChatContext';
import SimpleChatContainer from '../chat/SimpleChatContainer';

interface NavItem {
  icon: LucideIcon;
  label: string;
  href?: string;
  adminOnly?: boolean;
  action?: () => void;
}

const MobileNavigation: React.FC = () => {
  const location = useLocation();
  const { isAuthenticated, logout } = useAuth();
  const { isMobile } = useMobileOptimized();
  const [isChatOpen, setIsChatOpen] = useState(false);

  // Don't show on desktop or login page
  if (!isMobile || location.pathname === '/admin/login') {
    return null;
  }

  const publicNavItems: NavItem[] = [
    { icon: Wallet, label: 'Keuangan', href: '/' },
    { icon: Users, label: 'Anggota', href: '/members' },
    { icon: Calendar, label: 'Acara', href: '/events' },
    { icon: MessageSquare, label: 'Chat', action: () => setIsChatOpen(true) }
  ];

  const adminNavItems: NavItem[] = [
    { icon: Home, label: 'Dashboard', href: '/admin' },
    { icon: Users, label: 'Anggota', href: '/admin/members' },
    { icon: Calendar, label: 'Acara', href: '/admin/events' },
    { icon: TrendingDown, label: 'Pengeluaran', href: '/admin/expenses' },
    { icon: Settings, label: 'Setting', href: '/admin/dues-settings' }
  ];

  const navItems = isAuthenticated ? adminNavItems : publicNavItems;

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <motion.div
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 shadow-lg"
    >
      <div className="flex items-center justify-around px-2 py-2 safe-area-pb">
        {navItems.map((item, index) => {
          const isActive = item.href ? location.pathname === item.href : false;
          const key = item.href || `action-${index}`;

          if (item.action) {
            // Render button for action items (like Chat)
            return (
              <button
                key={key}
                type="button"
                onClick={item.action}
                className="flex flex-col items-center justify-center p-2 min-w-0 flex-1"
              >
                <motion.div
                  whileTap={{ scale: 0.9 }}
                  className="flex flex-col items-center justify-center space-y-1 text-gray-500 hover:text-[#B39DDB]"
                >
                  <div className="p-2 rounded-lg transition-colors hover:bg-gray-100">
                    <item.icon size={20} />
                  </div>
                  <span className="text-xs font-medium truncate max-w-full">
                    {item.label}
                  </span>
                </motion.div>
              </button>
            );
          }

          // Render link for navigation items
          return (
            <Link
              key={key}
              to={item.href!}
              className="flex flex-col items-center justify-center p-2 min-w-0 flex-1"
            >
              <motion.div
                whileTap={{ scale: 0.9 }}
                className={`flex flex-col items-center justify-center space-y-1 ${
                  isActive ? 'text-[#B39DDB]' : 'text-gray-500'
                }`}
              >
                <div className={`p-2 rounded-lg transition-colors ${
                  isActive ? 'bg-[#F3E5F5]' : 'hover:bg-gray-100'
                }`}>
                  <item.icon size={20} />
                </div>
                <span className="text-xs font-medium truncate max-w-full">
                  {item.label}
                </span>
              </motion.div>
            </Link>
          );
        })}
        
        {/* Logout button for admin */}
        {isAuthenticated && (
          <button
            type="button"
            onClick={handleLogout}
            className="flex flex-col items-center justify-center p-2 min-w-0 flex-1"
          >
            <motion.div
              whileTap={{ scale: 0.9 }}
              className="flex flex-col items-center justify-center space-y-1 text-red-500"
            >
              <div className="p-2 rounded-lg hover:bg-red-50 transition-colors">
                <LogOut size={20} />
              </div>
              <span className="text-xs font-medium">
                Keluar
              </span>
            </motion.div>
          </button>
        )}
      </div>

      {/* Chat Modal - Only for public users */}
      {!isAuthenticated && isChatOpen && (
        <SimpleChatProvider>
          <SimpleChatContainer
            isOpen={isChatOpen}
            onClose={() => setIsChatOpen(false)}
          />
        </SimpleChatProvider>
      )}
    </motion.div>
  );
};

export default MobileNavigation;
