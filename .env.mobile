# Mobile-specific environment variables
# Copy your Firebase config here for mobile builds

# Firebase Configuration for Mobile
VITE_FIREBASE_API_KEY=AIzaSyCyMds8m-KKOR4537ZvY4kipd4aFUbIgyc
VITE_FIREBASE_AUTH_DOMAIN=pemuda-psy.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=pemuda-psy
VITE_FIREBASE_STORAGE_BUCKET=pemuda-psy.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=792680197911
VITE_FIREBASE_APP_ID=1:792680197911:web:59e978bba5dea4dd419152
VITE_FIREBASE_MEASUREMENT_ID=G-LJJQNZMJ5L

# Mobile Platform Configuration
VITE_APP_PLATFORM=mobile
VITE_CAPACITOR_PLATFORM=android
VITE_APP_VERSION=1.0.0

# Performance Settings for Mobile
VITE_ENABLE_OFFLINE=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_CRASH_REPORTING=true

# Debug Settings
VITE_DEBUG_MODE=false
VITE_CONSOLE_LOGS=false
