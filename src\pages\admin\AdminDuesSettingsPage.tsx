import React, { useState, useEffect } from 'react';
// Interface untuk konfigurasi iuran
interface DuesConfiguration {
  pemuda_amount: number;
  pemudi_amount: number;
  pelajar_amount: number;
  notes: string;
}
import Loader from '../../components/Loader';
import PageTitle from '../../components/PageTitle';
import { Input } from '../../components/ui/input';
import { Textarea } from '../../components/ui/textarea';
import { Button } from '../../components/ui/button';
import { toast } from 'sonner';
import { Save } from 'lucide-react';
import { useDuesConfigContext } from '../../context/DuesConfigContext';

const AdminDuesSettingsPage: React.FC = () => {
  const { config: contextConfig, loading, updateConfig } = useDuesConfigContext();
  const [config, setConfig] = useState<Partial<DuesConfiguration>>({
    pemuda_amount: 0,
    pemudi_amount: 0,
    pelajar_amount: 0,
    notes: ''
  });
  const [isSaving, setIsSaving] = useState(false);

  // Update local state ketika context config berubah
  useEffect(() => {
    if (contextConfig) {
      setConfig({
        pemuda_amount: contextConfig.pemuda_amount,
        pemudi_amount: contextConfig.pemudi_amount,
        pelajar_amount: contextConfig.pelajar_amount,
        notes: contextConfig.notes
      });
    }
  }, [contextConfig]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setConfig(prev => ({
      ...prev,
      // Konversi ke angka jika ini adalah input amount
      [name]: name.includes('_amount') ? (value === '' ? 0 : Number(value)) : value
    }));
  };

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!contextConfig?.id) {
        toast.error("Gagal menyimpan: Konfigurasi belum dimuat.");
        return;
    }

    setIsSaving(true);
    try {
      await updateConfig(contextConfig.id, {
        pemuda_amount: config.pemuda_amount || 0,
        pemudi_amount: config.pemudi_amount || 0,
        pelajar_amount: config.pelajar_amount || 0,
        notes: config.notes || ''
      });

      toast.success('Konfigurasi iuran berhasil disimpan ke database!');
    } catch (err) {
      console.error('Error saving dues configuration:', err);
      toast.error('Gagal menyimpan konfigurasi ke database');
    } finally {
      setIsSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader text="Memuat konfigurasi dari database..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageTitle title="Pengaturan Iuran Anggota" />

      {!contextConfig && (
        <div className="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4" role="alert">
          <p className="font-bold">Info</p>
          <p>Membuat konfigurasi default. Silakan sesuaikan nilai iuran sesuai kebutuhan.</p>
        </div>
      )}

      <form onSubmit={handleSave} className="bg-white p-6 rounded-lg shadow space-y-4 max-w-2xl">
        <div>
          <label htmlFor="pemuda_amount" className="block text-sm font-medium text-gray-700 mb-1">
            Iuran Pemuda (Rp)
          </label>
          <Input
            id="pemuda_amount"
            name="pemuda_amount"
            type="number"
            value={config.pemuda_amount || ''}
            onChange={handleChange}
            placeholder="Contoh: 250000"
            required
          />
        </div>
        
        <div>
          <label htmlFor="pemudi_amount" className="block text-sm font-medium text-gray-700 mb-1">
            Iuran Pemudi (Rp)
          </label>
          <Input
            id="pemudi_amount"
            name="pemudi_amount"
            type="number"
            value={config.pemudi_amount || ''}
            onChange={handleChange}
            placeholder="Contoh: 150000"
            required
          />
        </div>
        
        <div>
          <label htmlFor="pelajar_amount" className="block text-sm font-medium text-gray-700 mb-1">
            Iuran Pelajar (Rp)
          </label>
          <Input
            id="pelajar_amount"
            name="pelajar_amount"
            type="number"
            value={config.pelajar_amount || ''}
            onChange={handleChange}
            placeholder="Contoh: 150000"
            required
          />
        </div>
        
        <div>
          <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
            Catatan Tambahan
          </label>
          <Textarea
            id="notes"
            name="notes"
            rows={4}
            value={config.notes || ''}
            onChange={handleChange}
            placeholder="Tambahkan catatan mengenai iuran jika ada..."
          />
        </div>
        
        <div className="flex justify-end">
          <Button type="submit" disabled={isSaving}>
            {isSaving ? (
              <span className="flex items-center">
                <Loader size="small" />
                <span className="ml-2">Menyimpan...</span>
              </span>
            ) : (
              <span className="flex items-center">
                <Save size={16} className="mr-2" /> Simpan Perubahan
              </span>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default AdminDuesSettingsPage; 