rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // DEVELOPMENT RULES - ALLOW ALL ACCESS (EXISTING - KEEP AS IS)
    // WARNING: These rules are for development only!
    // Change to secure rules before production deployment

    match /{document=**} {
      allow read, write: if true;
    }

    // CHAT SYSTEM RULES (TAMBAHAN UNTUK CHAT)
    // Users collection for chat system
    match /users/{userId} {
      allow read, write: if true; // Permissive untuk development
    }

    // Chat rooms collection
    match /chatRooms/{roomId} {
      allow read, write: if true; // Permissive untuk development
    }

    // Messages collection
    match /messages/{messageId} {
      allow read, write: if true; // Permissive untuk development
    }

    // Typing indicators
    match /typing/{roomId}/users/{userId} {
      allow read, write: if true; // Permissive untuk development
    }

    // Notifications
    match /notifications/{userId}/{notificationId} {
      allow read, write: if true; // Permissive untuk development
    }

    // SECURE PRODUCTION RULES (uncomment when ready for production)
    /*
    // Members collection - Auth required for all operations
    match /members/{memberId} {
      allow read, write: if request.auth != null;
    }

    // Events collection - Public read, auth required for write
    match /events/{eventId} {
      allow read: if true; // Public dapat melihat events
      allow write: if request.auth != null; // Hanya user login yang bisa edit
    }

    // Expenses collection - Auth required for all operations
    match /expenses/{expenseId} {
      allow read, write: if request.auth != null;
    }

    // Dues config collection - Public read, auth required for write
    match /dues_config/{configId} {
      allow read: if true; // Public dapat melihat konfigurasi iuran
      allow write: if request.auth != null; // Hanya admin yang bisa edit
    }

    // Chat system - Secure rules
    match /users/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }

    match /chatRooms/{roomId} {
      allow read: if request.auth != null &&
        (resource.data.type == 'general' ||
         request.auth.uid in resource.data.participants);
      allow create: if request.auth != null &&
        request.auth.uid == request.resource.data.createdBy;
      allow update: if request.auth != null &&
        (resource.data.type == 'general' ||
         request.auth.uid in resource.data.participants);
    }

    match /messages/{messageId} {
      allow read: if request.auth != null &&
        exists(/databases/$(database)/documents/chatRooms/$(resource.data.roomId));
      allow create: if request.auth != null &&
        request.auth.uid == request.resource.data.senderId;
      allow update: if request.auth != null &&
        (request.auth.uid == resource.data.senderId ||
         'reactions' in request.resource.data.diff(resource.data).affectedKeys());
    }

    match /typing/{roomId}/users/{userId} {
      allow read, write: if request.auth != null;
    }

    match /notifications/{userId}/{notificationId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Deny all other collections by default
    match /{document=**} {
      allow read, write: if false;
    }
    */
  }
}
