import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { handleError } from '../../utils/errorHandler';

interface MemberFormData {
  name: string;
  payment_status: 'paid' | 'unpaid';
  payment_amount: number;
  payment_date: string | null;
}

interface MemberFormProps {
  onSubmit: (memberData: Omit<MemberFormData, 'payment_date'> & { payment_date: string }) => Promise<void>;
  onCancel: () => void;
  initialData?: Partial<MemberFormData>;
  isEditing?: boolean;
  loading?: boolean;
}

const MemberForm: React.FC<MemberFormProps> = ({
  onSubmit,
  onCancel,
  initialData = {},
  isEditing = false,
  loading = false
}) => {
  const [formData, setFormData] = useState<MemberFormData>({
    name: initialData.name || '',
    payment_status: initialData.payment_status || 'unpaid',
    payment_amount: initialData.payment_amount || 0,
    payment_date: initialData.payment_date || null,
  });

  const formatToRupiah = (number: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(number);
  };

  const parseRupiahToNumber = (rupiahString: string) => {
    return Number(rupiahString.replace(/[^0-9]/g, ''));
  };

  const handleChange = (field: keyof MemberFormData, value: string | number) => {
    if (field === 'payment_amount') {
      const numericValue = typeof value === 'string' ? parseRupiahToNumber(value) : Number(value || 0);
      if (numericValue < 0) return;
      setFormData(prev => ({ ...prev, [field]: numericValue }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error("Nama anggota harus diisi");
      return;
    }

    if (formData.payment_amount <= 0) {
      toast.error("Jumlah pembayaran harus lebih dari 0");
      return;
    }

    try {
      const submitData = {
        ...formData,
        payment_date: formData.payment_date || new Date().toISOString().split('T')[0]
      };

      await onSubmit(submitData);
      
      if (!isEditing) {
        // Reset form for new member
        setFormData({
          name: '',
          payment_status: 'unpaid',
          payment_amount: 0,
          payment_date: null,
        });
      }
    } catch (error) {
      handleError(error, isEditing ? 'Edit Member' : 'Add Member');
    }
  };

  const isValid = formData.name.trim() && formData.payment_amount > 0;

  return (
    <div className="bg-white p-6 rounded-lg border-2 border-[#5D534B]">
      <h3 className="text-lg sm:text-xl font-bold mb-4">
        {isEditing ? 'Edit Anggota' : 'Tambah Anggota Baru'}
      </h3>
      
      <form onSubmit={handleSubmit}>
        <div className="space-y-4 sm:space-y-0 sm:grid sm:grid-cols-3 sm:gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium mb-2">Nama</label>
            <Input
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              className="neo-input text-base sm:text-sm h-12 sm:h-10"
              required
              placeholder="Masukkan nama anggota"
              disabled={loading}
            />
          </div>
          
          <div>
            <label htmlFor="member-status" className="block text-sm font-medium mb-2">Status</label>
            <select
              id="member-status"
              value={formData.payment_status}
              onChange={(e) => handleChange('payment_status', e.target.value as 'paid' | 'unpaid')}
              className="neo-input text-sm w-full h-12 sm:h-10"
              required
              disabled={loading}
              aria-label="Status pembayaran anggota"
              title="Pilih status pembayaran"
            >
              <option value="unpaid">Belum Lunas</option>
              <option value="paid">Lunas</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Jumlah</label>
            <Input
              type="text"
              inputMode="numeric"
              value={formatToRupiah(formData.payment_amount)}
              onChange={(e) => handleChange('payment_amount', e.target.value)}
              className="neo-input text-sm h-12 sm:h-10"
              required
              placeholder="Rp 0"
              disabled={loading}
              onKeyDown={(e) => {
                if (!/[0-9]/.test(e.key) && e.key !== 'Backspace' && e.key !== 'Delete' && e.key !== 'Tab' && e.key !== 'Enter') {
                  e.preventDefault();
                }
              }}
            />
          </div>
          
          {isEditing && (
            <div className="sm:col-span-3">
              <label className="block text-sm font-medium mb-2">Tanggal Pembayaran</label>
              <Input
                type="date"
                value={formData.payment_date || ''}
                onChange={(e) => handleChange('payment_date', e.target.value)}
                className="neo-input text-sm h-12 sm:h-10"
                disabled={loading}
              />
            </div>
          )}
        </div>
        
        <div className="flex justify-end space-x-2">
          <button
            type="submit"
            className="px-4 py-2 bg-[#9DE0D2] border-2 border-[#5D534B] rounded-lg font-medium disabled:opacity-50"
            disabled={!isValid || loading}
          >
            {loading ? 'Menyimpan...' : 'Simpan'}
          </button>
          <button
            type="button"
            className="px-4 py-2 bg-[#FF9898] border-2 border-[#5D534B] rounded-lg font-medium"
            onClick={onCancel}
            disabled={loading}
          >
            Batal
          </button>
        </div>
      </form>
    </div>
  );
};

export default MemberForm;
