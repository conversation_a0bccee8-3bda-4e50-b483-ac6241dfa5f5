import { toast } from 'sonner';

export interface ErrorInfo {
  code: string;
  message: string;
  userMessage: string;
  shouldRetry: boolean;
}

export const getErrorInfo = (error: unknown): ErrorInfo => {
  const errorObj = error as { code?: string; message?: string };

  // Firebase Auth Errors
  if (errorObj.code?.startsWith('auth/')) {
    switch (errorObj.code) {
      case 'auth/user-not-found':
        return {
          code: errorObj.code,
          message: errorObj.message || 'User not found',
          userMessage: 'Email tidak terdaftar',
          shouldRetry: false
        };
      case 'auth/wrong-password':
        return {
          code: errorObj.code,
          message: errorObj.message || 'Wrong password',
          userMessage: 'Password salah',
          shouldRetry: false
        };
      case 'auth/network-request-failed':
        return {
          code: errorObj.code,
          message: errorObj.message || 'Network request failed',
          userMessage: 'Koneksi internet bermasalah. Coba lagi.',
          shouldRetry: true
        };
      default:
        return {
          code: errorObj.code,
          message: errorObj.message || 'Auth error',
          userMessage: 'Gagal login. Coba lagi.',
          shouldRetry: true
        };
    }
  }

  // Firestore Errors
  if (errorObj.code?.startsWith('firestore/')) {
    switch (errorObj.code) {
      case 'firestore/permission-denied':
        return {
          code: errorObj.code,
          message: errorObj.message || 'Permission denied',
          userMessage: 'Akses ditolak. Login ulang.',
          shouldRetry: false
        };
      case 'firestore/unavailable':
        return {
          code: errorObj.code,
          message: errorObj.message || 'Service unavailable',
          userMessage: 'Server sedang bermasalah. Coba lagi.',
          shouldRetry: true
        };
      case 'firestore/deadline-exceeded':
        return {
          code: errorObj.code,
          message: errorObj.message || 'Deadline exceeded',
          userMessage: 'Koneksi timeout. Coba lagi.',
          shouldRetry: true
        };
      default:
        return {
          code: errorObj.code,
          message: errorObj.message || 'Firestore error',
          userMessage: 'Gagal menyimpan data. Coba lagi.',
          shouldRetry: true
        };
    }
  }

  // Validation Errors
  if (errorObj.message?.includes('VALIDASI GAGAL')) {
    return {
      code: 'validation-error',
      message: errorObj.message,
      userMessage: errorObj.message.replace('❌ VALIDASI GAGAL: ', ''),
      shouldRetry: false
    };
  }

  // Network Errors
  if (errorObj.message?.includes('network') || errorObj.message?.includes('fetch') || 
      errorObj.message?.includes('timeout') || errorObj.message?.includes('koneksi')) {
    return {
      code: 'network-error',
      message: errorObj.message,
      userMessage: 'Koneksi internet bermasalah. Periksa koneksi Anda.',
      shouldRetry: true
    };
  }

  // API Errors
  if (errorObj.code?.startsWith('api/') || errorObj.message?.includes('status code')) {
    const statusMatch = errorObj.message?.match(/status code (\d+)/);
    const status = statusMatch ? statusMatch[1] : '';

    if (status === '401' || status === '403') {
      return {
        code: `api/unauthorized-${status}`,
        message: errorObj.message || `API error ${status}`,
        userMessage: 'Sesi Anda telah berakhir. Silakan login kembali.',
        shouldRetry: false
      };
    }

    if (status === '404') {
      return {
        code: 'api/not-found',
        message: errorObj.message || 'Resource not found',
        userMessage: 'Data yang Anda cari tidak ditemukan.',
        shouldRetry: false
      };
    }

    if (status === '429') {
      return {
        code: 'api/rate-limited',
        message: errorObj.message || 'Too many requests',
        userMessage: 'Terlalu banyak permintaan. Coba lagi setelah beberapa saat.',
        shouldRetry: true
      };
    }

    if (status?.startsWith('5')) {
      return {
        code: 'api/server-error',
        message: errorObj.message || 'Server error',
        userMessage: 'Server sedang bermasalah. Coba lagi nanti.',
        shouldRetry: true
      };
    }

    return {
      code: 'api/error',
      message: errorObj.message || 'API error',
      userMessage: 'Terjadi kesalahan saat memproses permintaan. Coba lagi.',
      shouldRetry: true
    };
  }

  // Generic Error
  return {
    code: 'unknown-error',
    message: errorObj.message || 'Unknown error',
    userMessage: 'Terjadi kesalahan. Coba lagi atau hubungi admin.',
    shouldRetry: true
  };
};

/**
 * Menangani error dengan menampilkan pesan error yang ramah pengguna dan mencatat error
 * @param error - Error yang akan ditangani
 * @param context - Konteks di mana error terjadi (opsional)
 * @returns Informasi error yang sudah diproses
 */
export const handleError = (error: unknown, context?: string): ErrorInfo => {
  const errorInfo = getErrorInfo(error);
  
  console.error(`❌ ERROR in ${context || 'Unknown'}:`, {
    code: errorInfo.code,
    message: errorInfo.message,
    userMessage: errorInfo.userMessage,
    shouldRetry: errorInfo.shouldRetry,
    timestamp: new Date().toISOString()
  });

  // Show user-friendly message
  toast.error(errorInfo.userMessage, {
    duration: errorInfo.shouldRetry ? 5000 : 3000,
    action: errorInfo.shouldRetry ? {
      label: 'Coba Lagi',
      onClick: () => {
        // This will be handled by the calling component
        // User clicked retry - handled by calling component
      }
    } : undefined
  });

  return errorInfo;
};

/**
 * Menjalankan operasi dengan mekanisme retry otomatis
 * @param operation - Fungsi yang akan dijalankan dengan retry
 * @param maxRetries - Jumlah maksimum percobaan ulang (default: 3)
 * @param baseDelay - Waktu tunda dasar dalam milidetik (default: 1000)
 * @param context - Konteks operasi untuk keperluan logging
 * @returns Promise hasil dari operasi
 */
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
  context?: string
): Promise<T> => {
  let lastError: unknown;
  const operationName = context || 'unknown operation';
  const startTime = Date.now();

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await operation();

      // Log success if it wasn't on the first attempt
      if (attempt > 1) {
        // Operation succeeded after retry - using console.warn for ESLint compliance
        console.warn(`✅ ${operationName} succeeded after ${attempt} attempts in ${Date.now() - startTime}ms`);
      }

      return result;
    } catch (error) {
      lastError = error;
      const errorInfo = getErrorInfo(error);

      // Exponential backoff with jitter for better distributed retries
      const jitter = Math.random() * 0.3 + 0.85; // random between 0.85-1.15
      const delayMs = Math.min(baseDelay * Math.pow(2, attempt - 1) * jitter, 30000); // cap at 30 seconds

      console.warn(
        `⚠️ Attempt ${attempt}/${maxRetries} failed in ${operationName}:`,
        {
          message: errorInfo.userMessage,
          retryable: errorInfo.shouldRetry,
          nextRetryIn: attempt < maxRetries ? `${Math.round(delayMs)}ms` : 'N/A',
          elapsedTime: `${Date.now() - startTime}ms`
        }
      );

      // Don't retry if it's not a retryable error
      if (!errorInfo.shouldRetry) {
        console.error(`❌ ${operationName} failed with non-retryable error:`, errorInfo.message);
        throw error;
      }

      // Don't retry on last attempt
      if (attempt === maxRetries) {
        console.error(`❌ ${operationName} failed after ${maxRetries} attempts in ${Date.now() - startTime}ms`);
        break;
      }

      // Check if we're online before retrying
      if (!isOnline()) {
        // Waiting for network - using console.warn for ESLint compliance
        console.warn(`⏳ Waiting for network in ${operationName}...`);
        await waitForOnline();
        // Network restored - using console.warn for ESLint compliance
        console.warn(`🔄 Network connection restored, continuing with ${operationName}`);
      }

      // Wait before retry with exponential backoff
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
  }
  
  // All retries failed
  throw lastError;
};

/**
 * Memeriksa apakah aplikasi sedang online berdasarkan status browser
 * @returns Status koneksi internet
 */
export const isOnline = (): boolean => {
  return navigator.onLine;
};

export const waitForOnline = (): Promise<void> => {
  return new Promise((resolve) => {
    if (isOnline()) {
      resolve();
      return;
    }
    
    const handleOnline = () => {
      window.removeEventListener('online', handleOnline);
      resolve();
    };
    
    window.addEventListener('online', handleOnline);
  });
};
