import{v as e,o as l,C as t,n as i,k as o,z as n,Q as c,i as d}from"./index-B2aIgB1q.js";import{C as m,a as h}from"./card-CHb0Eu7t.js";const g=()=>{const a=[{title:"Kelola <PERSON>",description:"Tambah, edit, atau hapus data anggota",icon:l,href:"/admin/members",color:"#B39DDB",bgColor:"#F3E5F5"},{title:"Kelola Acara",description:"Buat dan atur acara organisasi",icon:t,href:"/admin/events",color:"#81C784",bgColor:"#E8F5E8"},{title:"<PERSON><PERSON><PERSON>",description:"Catat dan pantau pengeluaran",icon:i,href:"/admin/expenses",color:"#FF8A65",bgColor:"#FFF3E0"},{title:"Pengaturan Iuran",description:"Atur nominal dan periode iuran",icon:o,href:"/admin/dues-settings",color:"#4FC3F7",bgColor:"#E1F5FE"}];return e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:a.map((s,r)=>e.jsx(n.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:r*.1},whileHover:{scale:1.02},whileTap:{scale:.98},children:e.jsx(c,{to:s.href,className:"block h-full",children:e.jsx(m,{className:"neo-card h-full hover:shadow-lg transition-all duration-200 cursor-pointer group",children:e.jsxs(h,{className:"p-4 sm:p-6 h-full flex flex-col",children:[e.jsxs("div",{className:"flex items-start space-x-4 flex-1",children:[e.jsx("div",{className:"flex-shrink-0 w-12 h-12 sm:w-14 sm:h-14 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200",style:{backgroundColor:s.bgColor},children:e.jsx(s.icon,{size:24,className:"sm:w-7 sm:h-7",style:{color:s.color}})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"text-base sm:text-lg font-semibold text-[#5D534B] mb-1 sm:mb-2 group-hover:text-[#B39DDB] transition-colors",children:s.title}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-600 leading-relaxed",children:s.description})]})]}),e.jsx("div",{className:"flex justify-end mt-3 sm:mt-4",children:e.jsx("div",{className:"w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-gray-100 flex items-center justify-center group-hover:bg-[#B39DDB] group-hover:text-white transition-all duration-200",children:e.jsx(d,{size:14,className:"sm:w-4 sm:h-4"})})})]})})})},s.title))})};export{g as default};
