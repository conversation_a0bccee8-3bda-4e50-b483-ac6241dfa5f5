import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageSquare, Users, X, Plus } from 'lucide-react';
import { useChatContext } from '../../context/ChatContext';
import { useMobileApp } from '../../hooks/useMobileApp';
import ChatRoomList from './ChatRoomList';
import ChatMessages from './ChatMessages';
import ChatInput from './ChatInput';
import OnlineUsers from './OnlineUsers';
import { pageTransition } from '../../constants';

interface ChatContainerProps {
  isOpen: boolean;
  onClose: () => void;
}

const ChatContainer: React.FC<ChatContainerProps> = ({ isOpen, onClose }) => {
  const { currentRoom, isLoading } = useChatContext();
  const { isMobileApp } = useMobileApp();
  const [activeTab, setActiveTab] = useState<'chat' | 'users'>('chat');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Chat only available on mobile app
  if (!isMobileApp) {
    return (
      <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
        <div className="bg-[#F9F9F9] border-4 border-[#5D534B] shadow-[8px_8px_0px_#5D534B] rounded-lg p-8 text-center max-w-md">
          <MessageSquare className="w-16 h-16 text-[#5D534B] mx-auto mb-4" />
          <h3 className="text-xl font-black text-[#5D534B] mb-2">
            Chat Hanya di Mobile App
          </h3>
          <p className="text-[#5D534B] mb-4">
            Fitur chat hanya tersedia di aplikasi mobile DANAPEMUDA.
          </p>
          <button
            type="button"
            onClick={onClose}
            className="bg-[#FCE09B] text-[#5D534B] px-6 py-3 font-bold border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B] hover:shadow-[2px_2px_0px_#5D534B] transition-all rounded"
          >
            Tutup
          </button>
        </div>
      </div>
    );
  }

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          {...pageTransition}
          className="bg-[#F9F9F9] border-4 border-[#5D534B] shadow-[8px_8px_0px_#5D534B] rounded-lg w-full max-w-6xl h-[80vh] flex overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="absolute top-0 left-0 right-0 bg-[#FCE09B] border-b-4 border-[#5D534B] p-4 flex items-center justify-between z-10">
            <div className="flex items-center space-x-4">
              <MessageSquare className="w-6 h-6 text-[#5D534B]" />
              <h2 className="text-xl font-black text-[#5D534B]">
                {currentRoom?.name || 'DANAPEMUDA Chat'}
              </h2>
            </div>

            {/* Mobile Tab Switcher */}
            <div className="flex items-center space-x-2 md:hidden">
              <button
                onClick={() => setActiveTab('chat')}
                className={`px-3 py-1 text-sm font-bold border-2 border-[#5D534B] rounded ${
                  activeTab === 'chat'
                    ? 'bg-[#5D534B] text-white'
                    : 'bg-white text-[#5D534B]'
                }`}
              >
                Chat
              </button>
              <button
                onClick={() => setActiveTab('users')}
                className={`px-3 py-1 text-sm font-bold border-2 border-[#5D534B] rounded ${
                  activeTab === 'users'
                    ? 'bg-[#5D534B] text-white'
                    : 'bg-white text-[#5D534B]'
                }`}
              >
                Users
              </button>
            </div>

            <button
              onClick={onClose}
              title="Close chat"
              aria-label="Close chat"
              className="p-2 bg-[#FF9898] border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[1px_1px_0px_#5D534B] transition-all rounded"
            >
              <X className="w-5 h-5 text-[#5D534B]" />
            </button>
          </div>

          {/* Content */}
          <div className="flex w-full pt-20">
            {/* Sidebar - Rooms & Users */}
            <div className={`
              ${activeTab === 'chat' ? 'hidden md:flex' : 'flex md:flex'}
              flex-col w-full md:w-80 border-r-4 border-[#5D534B] bg-white
            `}>
              {/* Desktop Tabs */}
              <div className="hidden md:flex border-b-2 border-[#5D534B]">
                <button
                  onClick={() => setActiveTab('chat')}
                  className={`flex-1 p-3 font-bold border-r-2 border-[#5D534B] ${
                    activeTab === 'chat'
                      ? 'bg-[#FCE09B] text-[#5D534B]'
                      : 'bg-white text-[#5D534B] hover:bg-[#FCE09B]/20'
                  }`}
                >
                  <MessageSquare className="w-4 h-4 inline mr-2" />
                  Rooms
                </button>
                <button
                  onClick={() => setActiveTab('users')}
                  className={`flex-1 p-3 font-bold ${
                    activeTab === 'users'
                      ? 'bg-[#FCE09B] text-[#5D534B]'
                      : 'bg-white text-[#5D534B] hover:bg-[#FCE09B]/20'
                  }`}
                >
                  <Users className="w-4 h-4 inline mr-2" />
                  Users
                </button>
              </div>

              {/* Tab Content */}
              <div className="flex-1 overflow-hidden">
                {activeTab === 'chat' ? (
                  <ChatRoomList />
                ) : (
                  <OnlineUsers />
                )}
              </div>
            </div>

            {/* Main Chat Area */}
            <div className={`
              ${activeTab === 'chat' ? 'flex md:flex' : 'hidden md:flex'}
              flex-col flex-1
            `}>
              {isLoading ? (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin w-8 h-8 border-4 border-[#5D534B] border-t-transparent rounded-full mx-auto mb-4"></div>
                    <p className="text-[#5D534B] font-bold">Loading chat...</p>
                  </div>
                </div>
              ) : currentRoom ? (
                <>
                  <ChatMessages />
                  <ChatInput />
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center p-8">
                    <MessageSquare className="w-16 h-16 text-[#5D534B] mx-auto mb-4" />
                    <h3 className="text-xl font-black text-[#5D534B] mb-2">
                      Welcome to DANAPEMUDA Chat!
                    </h3>
                    <p className="text-[#5D534B] mb-4">
                      Select a chat room or start a conversation
                    </p>
                    <button className="bg-[#FCE09B] text-[#5D534B] px-6 py-3 font-bold border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B] hover:shadow-[2px_2px_0px_#5D534B] transition-all rounded">
                      <Plus className="w-4 h-4 inline mr-2" />
                      Start Chatting
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ChatContainer;
