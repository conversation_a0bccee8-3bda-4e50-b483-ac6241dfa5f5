import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Lock, User, ArrowRight, Shield, Eye, EyeOff, ArrowLeft } from 'lucide-react';
import { useAuth } from '../context/AuthContext';

const AdminLoginPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const navigate = useNavigate();
  const { login, isLoading } = useAuth();

  useEffect(() => {
    const savedEmail = localStorage.getItem('rememberedEmail');
    if (savedEmail) {
      setEmail(savedEmail);
      setRememberMe(true);
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    try {
      const success = await login(email, password);

      if (success) {
        if (rememberMe) {
          localStorage.setItem('rememberedEmail', email);
        } else {
          localStorage.removeItem('rememberedEmail');
        }

        navigate('/admin');
      } else {
        setError('Login gagal. Periksa email dan password Anda.');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error
        ? err.message
        : 'Login gagal. Periksa email dan password Anda.';
      setError(errorMessage);
    }
  };

  return (
    <div className="bg-[#F9F9F9] min-h-screen flex items-center justify-center p-4 sm:p-6">
      {/* Back to Home Button */}
      <motion.button
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
        onClick={() => navigate('/')}
        className="fixed top-4 left-4 sm:top-6 sm:left-6 z-10 flex items-center space-x-2 px-3 py-2 sm:px-4 sm:py-2 bg-white border-2 border-[#5D534B] rounded-full shadow-[4px_4px_0px_#5D534B] hover:shadow-[2px_2px_0px_#5D534B] hover:translate-x-[2px] hover:translate-y-[2px] transition-all duration-150 text-[#5D534B] font-medium text-sm"
      >
        <ArrowLeft size={16} />
        <span className="hidden sm:inline">Kembali ke Beranda</span>
        <span className="sm:hidden">Beranda</span>
      </motion.button>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md bg-white p-6 sm:p-8 rounded-2xl border-2 sm:border-4 border-[#5D534B] shadow-[6px_6px_0px_#5D534B] sm:shadow-[8px_8px_0px_#5D534B]"
      >
        <div className="flex flex-col items-center mb-6">
          <div className="w-16 h-16 bg-[#FCE09B] rounded-full border-4 border-[#5D534B] flex items-center justify-center mb-4">
            <Shield size={32} className="text-[#5D534B]" />
          </div>
          <h1 className="text-xl sm:text-2xl font-bold text-[#5D534B]">Admin Login</h1>
          <p className="text-[#5D534B]/80 text-sm text-center">Masuk untuk mengelola aplikasi DANAPEMUDA</p>
        </div>

        {error && (
          <div className="bg-[#FF9898]/30 border-2 border-[#FF9898] p-3 rounded-lg mb-4 text-[#5D534B] text-sm text-center">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="email" className="block text-sm font-medium mb-1 text-[#5D534B]">
              Email
            </label>
            <div className="relative">
              <User size={18} className="absolute left-3 top-1/2 -translate-y-1/2 text-[#5D534B]/70" />
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="w-full pl-10 pr-4 py-2 border-2 border-[#5D534B] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FCE09B] text-sm sm:text-base"
                required
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="password" className="block text-sm font-medium mb-1 text-[#5D534B]">
              Password
            </label>
            <div className="relative">
              <Lock size={18} className="absolute left-3 top-1/2 -translate-y-1/2 text-[#5D534B]/70" />
              <input
                id="password"
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Masukkan password"
                className="w-full pl-10 pr-10 py-2 border-2 border-[#5D534B] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FCE09B] text-sm sm:text-base"
                required
              />
              <button 
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-[#5D534B]/70 hover:text-[#5D534B] p-1"
                aria-label={showPassword ? "Sembunyikan sandi" : "Tampilkan sandi"}
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input 
                id="remember-me"
                name="remember-me"
                type="checkbox"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                className="h-4 w-4 text-[#FCE09B] focus:ring-[#f9d572] border-[#5D534B] rounded"
              />
              <label htmlFor="remember-me" className="ml-2 block text-xs sm:text-sm text-[#5D534B]">
                Ingat Saya
              </label>
            </div>
          </div>
          
          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-[#FCE09B] text-[#5D534B] font-bold py-2.5 sm:py-3 px-4 rounded-full border-2 sm:border-4 border-[#5D534B] hover:bg-[#ffd166] focus:outline-none focus:ring-2 focus:ring-[#FCE09B] focus:ring-opacity-50 shadow-[4px_4px_0px_#5D534B] flex items-center justify-center gap-2 disabled:opacity-70 active:shadow-[2px_2px_0px_#5D534B] active:translate-x-[2px] active:translate-y-[2px] transition-all duration-150"
          >
            {isLoading ? (
              <div className="w-5 h-5 border-2 border-[#5D534B] border-t-transparent rounded-full animate-spin"></div>
            ) : (
              <span className="flex items-center gap-1">
                Masuk <ArrowRight size={18} />
              </span>
            )}
          </button>
        </form>
        
        <div className="mt-6 text-center">
          <p className="text-xs text-[#5D534B]/60">
            © 2023 DANAPEMUDA. All rights reserved.
          </p>
        </div>
      </motion.div>
    </div>
  );
};

export default AdminLoginPage; 