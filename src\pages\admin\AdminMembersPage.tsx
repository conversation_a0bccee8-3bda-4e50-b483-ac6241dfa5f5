import { useState } from 'react';
import { Plus } from 'lucide-react';
import { toast } from 'sonner';
import Loader from '../../components/Loader';
import { useMembersContext, MemberData } from '../../context/MembersContext';
import { handleError, withRetry } from '../../utils/errorHandler';
import { useMobileOptimized } from '../../hooks/useMobileOptimized';
import MobileHeader from '../../components/mobile/MobileHeader';
import Pagination from '../../components/Pagination';
import MemberForm from '../../components/admin/MemberForm';
import MemberTable from '../../components/admin/MemberTable';
import MemberSearchFilter from '../../components/admin/MemberSearchFilter';
import MembersMobileView from '../../components/admin/MembersMobileView';


const AdminMembersPage = () => {
  const {
    members,
    loading,
    currentPage,
    totalPages,
    itemsPerPage,
    totalMembers,
    addMember,
    updateMember,
    deleteMember,
    toggleMemberStatus,
    setPage,
    searchMembers,
    filterMembers
  } = useMembersContext();
  const { isMobile } = useMobileOptimized();
  interface EditValues {
    name?: string;
    payment_status?: 'paid' | 'unpaid';
    payment_amount?: number;
    payment_date?: string;
  }

  const [editingId, setEditingId] = useState<string | null>(null);
  const [editValues, setEditValues] = useState<EditValues>({});
  const [isAdding, setIsAdding] = useState(false);
  const [toggleLoading, setToggleLoading] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState(false);


  const handleEdit = (member: MemberData) => {
    setEditingId(member.id);
    setEditValues({
      ...member,
      payment_date: member.payment_date || undefined
    });
  };

  const handleChange = (field: string, value: string | number) => {
    setEditValues(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleMobileDelete = (member: MemberData) => {
    handleDelete(member.id);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Apakah Anda yakin ingin menghapus anggota ini?')) return;

    try {
      setActionLoading(true);
      await deleteMember(id);
      toast.success("Data anggota berhasil dihapus");
    } catch (error) {
      handleError(error, 'AdminMembersPage (admin)');
      toast.error("Gagal menghapus data anggota");
    } finally {
      setActionLoading(false);
    }
  };

  const handleAddMember = async (memberData: Omit<MemberData, 'id'>) => {
    try {
      setActionLoading(true);

      await withRetry(
        () => addMember(memberData),
        3,
        1000,
        'Add Member'
      );

      setIsAdding(false);
      toast.success("✅ Anggota berhasil ditambahkan ke database");
    } catch (error) {
      handleError(error, 'Add Member');
      throw error; // Re-throw untuk handling di MemberForm
    } finally {
      setActionLoading(false);
    }
  };





  const handleToggleStatus = async (member: MemberData) => {
    try {
      setToggleLoading(member.id);
      await toggleMemberStatus(member.id);
      toast.success("Status pembayaran berhasil diperbarui di database");
    } catch (error) {
      handleError(error, 'AdminMembersPage (admin)');
      toast.error("Gagal memperbarui status pembayaran di database");
    } finally {
      setToggleLoading(null);
    }
  };

  const handleEditSubmit = async () => {
    try {
      if (!editingId) return;

      setActionLoading(true);
      await updateMember(editingId, editValues);
      setEditingId(null);
      toast.success("Data anggota berhasil diperbarui di database");
    } catch (error) {
      handleError(error, 'AdminMembersPage (admin)');
      toast.error("Gagal memperbarui data anggota di database");
    } finally {
      setActionLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader size="medium" variant="secondary" text="Memuat Data Anggota dari Database..." />
      </div>
    );
  }



  return (
    <>
      {/* Mobile Header */}
      {isMobile && (
        <MobileHeader
          title="Kelola Anggota"
          showBackButton={true}
        />
      )}

      <div className={`space-y-6 ${isMobile ? 'pt-20 pb-24 px-4' : ''}`}>
        {/* Header */}
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-black">Kelola Anggota</h2>
          {!isMobile && (
            <button
              type="button"
              className="neo-button-green text-sm flex items-center"
              onClick={() => setIsAdding(true)}
            >
              <Plus size={16} className="mr-2" />
              Tambah Anggota
            </button>
          )}
        </div>

        {/* Mobile Add Button */}
        {isMobile && (
          <button
            type="button"
            onClick={() => setIsAdding(true)}
            className="w-full flex items-center justify-center px-4 py-3 bg-[#B39DDB] text-white rounded-lg hover:bg-[#A389D1] transition-colors font-medium tap-target"
          >
            <Plus size={20} className="mr-2" />
            Tambah Anggota Baru
          </button>
        )}

      {/* Search and Filter */}
      <MemberSearchFilter
        totalMembers={totalMembers}
        onSearch={searchMembers}
        onFilter={filterMembers}
      />

      {/* Add Member Form */}
      {isAdding && (
        <MemberForm
          onSubmit={handleAddMember}
          onCancel={() => setIsAdding(false)}
          loading={actionLoading}
        />
      )}

        {/* Mobile View */}
        {isMobile ? (
          <MembersMobileView
            members={members}
            editingId={editingId}
            editValues={editValues}
            toggleLoading={toggleLoading}
            actionLoading={actionLoading}
            onEdit={handleEdit}
            onDelete={handleMobileDelete}
            onToggleStatus={handleToggleStatus}
            onSaveEdit={handleEditSubmit}
            onCancelEdit={() => setEditingId(null)}
            onChange={handleChange}
          />
        ) : (
          /* Desktop Table View */
          <MemberTable
            members={members}
            editingId={editingId}
            editValues={editValues}
            toggleLoading={toggleLoading}
            actionLoading={actionLoading}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onToggleStatus={handleToggleStatus}
            onSaveEdit={handleEditSubmit}
            onCancelEdit={() => setEditingId(null)}
            onChange={handleChange}
          />
        )}

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalMembers}
          itemsPerPage={itemsPerPage}
          onPageChange={setPage}
        />
      </div>
    </>
  );
};

export default AdminMembersPage;
