import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { User, Edit3, Save, X } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { useMobileApp } from '../../hooks/useMobileApp';

interface MobileUserProfileProps {
  isOpen: boolean;
  onClose: () => void;
}

const MobileUserProfile: React.FC<MobileUserProfileProps> = ({ isOpen, onClose }) => {
  const { user, updateProfile } = useAuth();
  const { isMobileApp } = useMobileApp();
  const [isEditing, setIsEditing] = useState(false);
  const [displayName, setDisplayName] = useState(user?.displayName || '');
  const [isLoading, setIsLoading] = useState(false);

  // Only show on mobile app
  if (!isMobileApp || !isOpen) return null;

  const handleSave = async () => {
    if (!displayName.trim()) return;

    setIsLoading(true);
    try {
      await updateProfile({ displayName: displayName.trim() });
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Gagal mengupdate nama. Silakan coba lagi.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setDisplayName(user?.displayName || '');
    setIsEditing(false);
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-[#F9F9F9] border-4 border-[#5D534B] shadow-[8px_8px_0px_#5D534B] rounded-lg w-full max-w-md overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="bg-[#FCE09B] border-b-4 border-[#5D534B] p-4 flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-[#5D534B] border-2 border-[#5D534B] rounded-full flex items-center justify-center">
                <User className="w-6 h-6 text-[#FCE09B]" />
              </div>
              <h2 className="text-xl font-black text-[#5D534B]">Profil Saya</h2>
            </div>
            <button
              type="button"
              onClick={onClose}
              title="Close profile"
              aria-label="Close profile"
              className="p-2 bg-[#FF9898] border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[1px_1px_0px_#5D534B] transition-all rounded"
            >
              <X className="w-5 h-5 text-[#5D534B]" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6 space-y-6">
            {/* Avatar */}
            <div className="text-center">
              <div className="w-20 h-20 bg-[#FCE09B] border-4 border-[#5D534B] rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl font-black text-[#5D534B]">
                  {(user?.displayName || 'User').charAt(0).toUpperCase()}
                </span>
              </div>
              <p className="text-sm text-[#5D534B]/70">DANAPEMUDA Member</p>
            </div>

            {/* Name Field */}
            <div className="space-y-2">
              <label className="block text-sm font-bold text-[#5D534B]">
                Nama Lengkap
              </label>
              {isEditing ? (
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={displayName}
                    onChange={(e) => setDisplayName(e.target.value)}
                    className="flex-1 p-3 border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] rounded focus:outline-none focus:shadow-[4px_4px_0px_#5D534B] transition-all"
                    placeholder="Masukkan nama lengkap"
                    disabled={isLoading}
                  />
                  <button
                    type="button"
                    onClick={handleSave}
                    disabled={isLoading || !displayName.trim()}
                    className="p-3 bg-[#9DE0D2] border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[4px_4px_0px_#5D534B] transition-all rounded disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <div className="w-5 h-5 border-2 border-[#5D534B] border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <Save className="w-5 h-5 text-[#5D534B]" />
                    )}
                  </button>
                  <button
                    type="button"
                    onClick={handleCancel}
                    disabled={isLoading}
                    title="Cancel editing"
                    aria-label="Cancel editing"
                    className="p-3 bg-[#FF9898] border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[4px_4px_0px_#5D534B] transition-all rounded disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <X className="w-5 h-5 text-[#5D534B]" />
                  </button>
                </div>
              ) : (
                <div className="flex items-center justify-between p-3 bg-white border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] rounded">
                  <span className="font-medium text-[#5D534B]">
                    {user?.displayName || 'Belum diatur'}
                  </span>
                  <button
                    type="button"
                    onClick={() => setIsEditing(true)}
                    title="Edit profile"
                    aria-label="Edit profile"
                    className="p-2 bg-[#FCE09B] border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[4px_4px_0px_#5D534B] transition-all rounded"
                  >
                    <Edit3 className="w-4 h-4 text-[#5D534B]" />
                  </button>
                </div>
              )}
            </div>



            {/* Info */}
            <div className="bg-[#FCE09B]/20 border-2 border-[#5D534B] rounded p-4">
              <h4 className="font-bold text-[#5D534B] mb-2">Informasi</h4>
              <ul className="text-sm text-[#5D534B]/80 space-y-1">
                <li>• Nama akan ditampilkan di chat</li>
                <li>• Akun tidak dapat logout di mobile</li>
                <li>• Chat hanya tersedia di mobile app</li>
              </ul>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default MobileUserProfile;
