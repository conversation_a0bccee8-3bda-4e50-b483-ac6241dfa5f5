import{v as e,a as V,a0 as Y,t as p,J as y,O as j,S as Z,U as P,z as ee,u as ae,A as te,w as se,i as H}from"./index-F6RtwXXX.js";import{h as B,w as ne}from"./errorHandler-C3B1lzgV.js";import{M as le}from"./MobileHeader-C7XjsfCK.js";import{c as re,b as ie,d as de,L as I,C as oe,a as ce}from"./loader-circle-o97bjoGo.js";import{I as w}from"./input-Cv2V0cpu.js";import{f as J,a as me}from"./formatters-LGS2Cxr7.js";import{S as q,T as ue}from"./trash-BvkSBYEx.js";import{T as pe}from"./trash-2-DeYwRfE3.js";import"./arrow-left-C9lccUgQ.js";const xe=({currentPage:t,totalPages:n,totalItems:l,itemsPerPage:d,onPageChange:r,className:o=""})=>{const x=(t-1)*d+1,b=Math.min(t*d,l),h=()=>{const c=[],a=[];for(let s=Math.max(2,t-2);s<=Math.min(n-1,t+2);s++)c.push(s);return t-2>2?a.push(1,"..."):a.push(1),a.push(...c),t+2<n-1?a.push("...",n):a.push(n),a};return n<=1?null:e.jsxs("div",{className:`flex flex-col sm:flex-row items-center justify-between gap-4 ${o}`,children:[e.jsxs("div",{className:"text-sm text-[#5D534B] order-2 sm:order-1",children:["Menampilkan ",x,"-",b," dari ",l," data"]}),e.jsxs("div",{className:"flex items-center space-x-1 order-1 sm:order-2",children:[e.jsx("button",{type:"button",onClick:()=>r(1),disabled:t===1,className:"p-2 rounded-lg border-2 border-[#5D534B] bg-white hover:bg-[#F9F9F9] disabled:opacity-50 disabled:cursor-not-allowed transition-colors",title:"Halaman pertama",children:e.jsx(re,{size:16})}),e.jsx("button",{type:"button",onClick:()=>r(t-1),disabled:t===1,className:"p-2 rounded-lg border-2 border-[#5D534B] bg-white hover:bg-[#F9F9F9] disabled:opacity-50 disabled:cursor-not-allowed transition-colors",title:"Halaman sebelumnya",children:e.jsx(V,{size:16})}),e.jsx("div",{className:"hidden sm:flex items-center space-x-1",children:h().map((m,c)=>e.jsx(Y.Fragment,{children:m==="..."?e.jsx("span",{className:"px-3 py-2 text-[#5D534B]",children:"..."}):e.jsx("button",{type:"button",onClick:()=>r(m),className:`px-3 py-2 rounded-lg border-2 border-[#5D534B] transition-colors ${t===m?"bg-[#9DE0D2] text-[#5D534B] font-bold":"bg-white hover:bg-[#F9F9F9] text-[#5D534B]"}`,children:m})},c))}),e.jsxs("div",{className:"sm:hidden px-3 py-2 border-2 border-[#5D534B] rounded-lg bg-[#9DE0D2] text-[#5D534B] font-bold",children:[t," / ",n]}),e.jsx("button",{type:"button",onClick:()=>r(t+1),disabled:t===n,className:"p-2 rounded-lg border-2 border-[#5D534B] bg-white hover:bg-[#F9F9F9] disabled:opacity-50 disabled:cursor-not-allowed transition-colors",title:"Halaman selanjutnya",children:e.jsx(ie,{size:16})}),e.jsx("button",{type:"button",onClick:()=>r(n),disabled:t===n,className:"p-2 rounded-lg border-2 border-[#5D534B] bg-white hover:bg-[#F9F9F9] disabled:opacity-50 disabled:cursor-not-allowed transition-colors",title:"Halaman terakhir",children:e.jsx(de,{size:16})})]})]})},be=({onSubmit:t,onCancel:n,initialData:l={},isEditing:d=!1,loading:r=!1})=>{const[o,x]=p.useState({name:l.name||"",payment_status:l.payment_status||"unpaid",payment_amount:l.payment_amount||0,payment_date:l.payment_date||null}),b=s=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0,maximumFractionDigits:0}).format(s),h=s=>Number(s.replace(/[^0-9]/g,"")),m=(s,i)=>{if(s==="payment_amount"){const g=typeof i=="string"?h(i):Number(i||0);if(g<0)return;x(N=>({...N,[s]:g}))}else x(g=>({...g,[s]:i}))},c=async s=>{if(s.preventDefault(),!o.name.trim()){y.error("Nama anggota harus diisi");return}if(o.payment_amount<=0){y.error("Jumlah pembayaran harus lebih dari 0");return}try{const i={...o,payment_date:o.payment_date||new Date().toISOString().split("T")[0]};await t(i),d||x({name:"",payment_status:"unpaid",payment_amount:0,payment_date:null})}catch(i){B(i,d?"Edit Member":"Add Member")}},a=o.name.trim()&&o.payment_amount>0;return e.jsxs("div",{className:"bg-white p-6 rounded-lg border-2 border-[#5D534B]",children:[e.jsx("h3",{className:"text-lg sm:text-xl font-bold mb-4",children:d?"Edit Anggota":"Tambah Anggota Baru"}),e.jsxs("form",{onSubmit:c,children:[e.jsxs("div",{className:"space-y-4 sm:space-y-0 sm:grid sm:grid-cols-3 sm:gap-4 mb-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Nama"}),e.jsx(w,{value:o.name,onChange:s=>m("name",s.target.value),className:"neo-input text-base sm:text-sm h-12 sm:h-10",required:!0,placeholder:"Masukkan nama anggota",disabled:r})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"member-status",className:"block text-sm font-medium mb-2",children:"Status"}),e.jsxs("select",{id:"member-status",value:o.payment_status,onChange:s=>m("payment_status",s.target.value),className:"neo-input text-sm w-full h-12 sm:h-10",required:!0,disabled:r,"aria-label":"Status pembayaran anggota",title:"Pilih status pembayaran",children:[e.jsx("option",{value:"unpaid",children:"Belum Lunas"}),e.jsx("option",{value:"paid",children:"Lunas"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Jumlah"}),e.jsx(w,{type:"text",inputMode:"numeric",value:b(o.payment_amount),onChange:s=>m("payment_amount",s.target.value),className:"neo-input text-sm h-12 sm:h-10",required:!0,placeholder:"Rp 0",disabled:r,onKeyDown:s=>{!/[0-9]/.test(s.key)&&s.key!=="Backspace"&&s.key!=="Delete"&&s.key!=="Tab"&&s.key!=="Enter"&&s.preventDefault()}})]}),d&&e.jsxs("div",{className:"sm:col-span-3",children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Tanggal Pembayaran"}),e.jsx(w,{type:"date",value:o.payment_date||"",onChange:s=>m("payment_date",s.target.value),className:"neo-input text-sm h-12 sm:h-10",disabled:r})]})]}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx("button",{type:"submit",className:"px-4 py-2 bg-[#9DE0D2] border-2 border-[#5D534B] rounded-lg font-medium disabled:opacity-50",disabled:!a||r,children:r?"Menyimpan...":"Simpan"}),e.jsx("button",{type:"button",className:"px-4 py-2 bg-[#FF9898] border-2 border-[#5D534B] rounded-lg font-medium",onClick:n,disabled:r,children:"Batal"})]})]})]})},G=p.forwardRef(({className:t,...n},l)=>e.jsx("div",{className:"relative w-full overflow-auto",children:e.jsx("table",{ref:l,className:j("w-full caption-bottom text-sm",t),...n})}));G.displayName="Table";const K=p.forwardRef(({className:t,...n},l)=>e.jsx("thead",{ref:l,className:j("[&_tr]:border-b",t),...n}));K.displayName="TableHeader";const O=p.forwardRef(({className:t,...n},l)=>e.jsx("tbody",{ref:l,className:j("[&_tr:last-child]:border-0",t),...n}));O.displayName="TableBody";const he=p.forwardRef(({className:t,...n},l)=>e.jsx("tfoot",{ref:l,className:j("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...n}));he.displayName="TableFooter";const T=p.forwardRef(({className:t,...n},l)=>e.jsx("tr",{ref:l,className:j("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...n}));T.displayName="TableRow";const D=p.forwardRef(({className:t,...n},l)=>e.jsx("th",{ref:l,className:j("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...n}));D.displayName="TableHead";const _=p.forwardRef(({className:t,...n},l)=>e.jsx("td",{ref:l,className:j("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...n}));_.displayName="TableCell";const ge=p.forwardRef(({className:t,...n},l)=>e.jsx("caption",{ref:l,className:j("mt-4 text-sm text-muted-foreground",t),...n}));ge.displayName="TableCaption";const ye=({members:t,editingId:n,editValues:l,toggleLoading:d,actionLoading:r,onEdit:o,onDelete:x,onToggleStatus:b,onSaveEdit:h,onCancelEdit:m,onChange:c})=>e.jsx("div",{className:"hidden md:block overflow-x-auto",children:e.jsxs(G,{className:"neo-table min-w-full",children:[e.jsx(K,{children:e.jsxs(T,{children:[e.jsx(D,{className:"text-sm whitespace-nowrap",children:"Nama"}),e.jsx(D,{className:"text-sm whitespace-nowrap",children:"Status"}),e.jsx(D,{className:"text-sm whitespace-nowrap",children:"Jumlah"}),e.jsx(D,{className:"text-sm whitespace-nowrap",children:"Tanggal"}),e.jsx(D,{className:"text-sm text-right whitespace-nowrap",children:"Aksi"})]})}),e.jsx(O,{children:t.map(a=>e.jsxs(T,{children:[e.jsx(_,{className:"text-sm",children:n===a.id?e.jsx(w,{value:l.name||"",onChange:s=>c(a.id,"name",s.target.value),className:"neo-input text-sm",placeholder:"Nama anggota"}):e.jsx("span",{className:"font-medium",children:a.name})}),e.jsx(_,{className:"text-sm",children:n===a.id?e.jsxs("select",{id:`table-status-${a.id}`,value:l.payment_status||"unpaid",onChange:s=>c(a.id,"payment_status",s.target.value),className:"neo-input text-sm","aria-label":"Status pembayaran anggota",title:"Pilih status pembayaran",children:[e.jsx("option",{value:"paid",children:"Lunas"}),e.jsx("option",{value:"unpaid",children:"Belum Lunas"})]}):e.jsxs("button",{type:"button",onClick:()=>b(a),disabled:d===a.id,className:`px-2.5 py-1 rounded-full text-xs sm:text-sm font-medium cursor-pointer transition-all duration-150 min-w-[70px] text-center inline-flex items-center justify-center border-2 border-[#5D534B] bg-white shadow-[3px_3px_0px_rgba(93,83,75,0.7)] active:shadow-[1px_1px_0px_rgba(93,83,75,0.7)] active:translate-x-[2px] active:translate-y-[2px] ${a.payment_status==="paid"?"text-green-600 hover:bg-green-50":"text-red-600 hover:bg-red-50"} ${d===a.id?"opacity-50 cursor-not-allowed":""}`,children:[d===a.id&&e.jsx(I,{className:"h-4 w-4 animate-spin inline mr-1"}),a.payment_status==="paid"?"Lunas":"Belum"]})}),e.jsx(_,{className:"text-sm",children:n===a.id?e.jsx(w,{type:"number",value:l.payment_amount||0,onChange:s=>c(a.id,"payment_amount",Number(s.target.value)),className:"neo-input text-sm",placeholder:"Jumlah"}):e.jsx("span",{className:"font-semibold",children:J(a.payment_amount)})}),e.jsx(_,{className:"text-sm",children:n===a.id?e.jsx(w,{type:"date",value:l.payment_date||"",onChange:s=>c(a.id,"payment_date",s.target.value),className:"neo-input text-sm"}):e.jsx("span",{className:"text-gray-600",children:a.payment_date||"-"})}),e.jsx(_,{className:"text-right",children:n===a.id?e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx("button",{type:"button",onClick:h,disabled:r,className:"p-2 bg-[#9DE0D2] border-2 border-[#5D534B] rounded-lg hover:bg-[#8DD4C6] transition-colors disabled:opacity-50",title:"Simpan perubahan",children:r?e.jsx(I,{className:"h-4 w-4 animate-spin"}):e.jsx(Z,{size:16})}),e.jsx("button",{type:"button",onClick:m,disabled:r,className:"p-2 bg-[#FF9898] border-2 border-[#5D534B] rounded-lg hover:bg-[#FF8A8A] transition-colors disabled:opacity-50",title:"Batal edit",children:"❌"})]}):e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx("button",{type:"button",onClick:()=>o(a),className:"p-2 bg-[#9DE0D2] border-2 border-[#5D534B] rounded-lg hover:bg-[#8DD4C6] transition-colors",title:"Edit anggota",children:e.jsx(q,{size:16})}),e.jsx("button",{type:"button",onClick:()=>x(a.id),className:"p-2 bg-[#FF9898] border-2 border-[#5D534B] rounded-lg hover:bg-[#FF8A8A] transition-colors",title:"Hapus anggota",children:e.jsx(ue,{size:16})})]})})]},a.id))})]})}),fe=({totalMembers:t,onSearch:n,onFilter:l})=>e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 items-center justify-between bg-white p-4 rounded-lg border-2 border-[#5D534B]",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2 w-full sm:w-auto",children:[e.jsx("input",{type:"text",placeholder:"🔍 Cari nama anggota...",onChange:d=>n(d.target.value),className:"px-4 py-3 sm:py-2 border-2 border-[#5D534B] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#9DE0D2] text-base sm:text-sm w-full sm:w-64"}),e.jsxs("select",{onChange:d=>l(d.target.value),className:"px-4 py-3 sm:py-2 border-2 border-[#5D534B] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#9DE0D2] text-base sm:text-sm","aria-label":"Filter status pembayaran",children:[e.jsx("option",{value:"all",children:"📋 Semua Status"}),e.jsx("option",{value:"paid",children:"✅ Lunas"}),e.jsx("option",{value:"unpaid",children:"❌ Belum Lunas"})]})]}),e.jsxs("div",{className:"text-sm text-[#5D534B] font-medium bg-[#9DE0D2] px-3 py-2 rounded-lg border-2 border-black",children:["Total: ",t," anggota"]})]}),je=({members:t,editingId:n,editValues:l,toggleLoading:d,actionLoading:r,onEdit:o,onDelete:x,onToggleStatus:b,onSaveEdit:h,onCancelEdit:m,onChange:c})=>t.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(P,{size:48,className:"mx-auto text-gray-400 mb-4"}),e.jsx("p",{className:"text-gray-500 text-lg",children:"Belum ada anggota"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Tambahkan anggota pertama untuk memulai"})]}):e.jsx("div",{className:"space-y-4",children:t.map((a,s)=>e.jsx(ee.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:s*.1},className:"bg-white rounded-lg border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B] overflow-hidden",children:n===a.id?e.jsxs("div",{className:"p-4 space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:`name-${a.id}`,className:"block text-sm font-medium text-gray-700 mb-1",children:"Nama"}),e.jsx("input",{id:`name-${a.id}`,type:"text",value:l.name||"",onChange:i=>c("name",i.target.value),className:"w-full px-3 py-2 border-2 border-[#5D534B] rounded-md focus:outline-none focus:ring-2 focus:ring-[#B39DDB] focus:border-[#B39DDB] shadow-[2px_2px_0px_#5D534B]",placeholder:"Masukkan nama anggota"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:`status-${a.id}`,className:"block text-sm font-medium text-gray-700 mb-1",children:"Status Pembayaran"}),e.jsxs("select",{id:`status-${a.id}`,value:l.payment_status||"",onChange:i=>c("payment_status",i.target.value),className:"w-full px-3 py-2 border-2 border-[#5D534B] rounded-md focus:outline-none focus:ring-2 focus:ring-[#B39DDB] focus:border-[#B39DDB] shadow-[2px_2px_0px_#5D534B]",title:"Pilih status pembayaran",children:[e.jsx("option",{value:"paid",children:"Lunas"}),e.jsx("option",{value:"unpaid",children:"Belum Lunas"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:`amount-${a.id}`,className:"block text-sm font-medium text-gray-700 mb-1",children:"Jumlah Iuran"}),e.jsx("input",{id:`amount-${a.id}`,type:"number",value:l.payment_amount||"",onChange:i=>c("payment_amount",parseInt(i.target.value)||0),className:"w-full px-3 py-2 border-2 border-[#5D534B] rounded-md focus:outline-none focus:ring-2 focus:ring-[#B39DDB] focus:border-[#B39DDB] shadow-[2px_2px_0px_#5D534B]",placeholder:"Masukkan jumlah iuran"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:`date-${a.id}`,className:"block text-sm font-medium text-gray-700 mb-1",children:"Tanggal Pembayaran"}),e.jsx("input",{id:`date-${a.id}`,type:"date",value:l.payment_date||"",onChange:i=>c("payment_date",i.target.value),className:"w-full px-3 py-2 border-2 border-[#5D534B] rounded-md focus:outline-none focus:ring-2 focus:ring-[#B39DDB] focus:border-[#B39DDB] shadow-[2px_2px_0px_#5D534B]",placeholder:"Pilih tanggal pembayaran"})]}),e.jsxs("div",{className:"flex space-x-2 pt-2",children:[e.jsx("button",{type:"button",onClick:()=>h(),disabled:r,className:"flex-1 bg-green-500 text-white px-4 py-2 rounded-md border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:bg-green-600 hover:shadow-[1px_1px_0px_#5D534B] transition-all disabled:opacity-50 text-sm font-medium",children:r?"Menyimpan...":"Simpan"}),e.jsx("button",{type:"button",onClick:m,className:"flex-1 bg-gray-500 text-white px-4 py-2 rounded-md border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:bg-gray-600 hover:shadow-[1px_1px_0px_#5D534B] transition-all text-sm font-medium",children:"Batal"})]})]}):e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-semibold text-gray-900 text-lg",children:a.name}),e.jsx("div",{className:"flex items-center mt-2",children:e.jsxs("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-xs font-bold border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] ${a.payment_status==="paid"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:[a.payment_status==="paid"?e.jsx(oe,{size:14,className:"mr-1"}):e.jsx(ce,{size:14,className:"mr-1"}),a.payment_status==="paid"?"Lunas":"Belum Lunas"]})})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{type:"button",onClick:()=>o(a),className:"p-2 text-blue-600 hover:bg-blue-50 rounded-full border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[1px_1px_0px_#5D534B] transition-all bg-white","aria-label":`Edit member ${a.name}`,title:`Edit member ${a.name}`,children:e.jsx(q,{size:16})}),e.jsx("button",{type:"button",onClick:()=>x(a),disabled:r,className:"p-2 text-red-600 hover:bg-red-50 rounded-full border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[1px_1px_0px_#5D534B] transition-all disabled:opacity-50 bg-white","aria-label":`Delete member ${a.name}`,title:`Delete member ${a.name}`,children:e.jsx(pe,{size:16})})]})]}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Jumlah Iuran:"}),e.jsx("span",{className:"font-medium text-gray-900",children:J(a.payment_amount)})]}),a.payment_date&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Tanggal Bayar:"}),e.jsx("span",{className:"font-medium text-gray-900",children:me(a.payment_date)})]})]}),e.jsx("div",{className:"mt-4 pt-3 border-t-2 border-[#5D534B]",children:e.jsx("button",{type:"button",onClick:()=>b(a),disabled:d===a.id,className:`w-full px-4 py-3 rounded-md text-sm font-bold border-2 border-[#5D534B] shadow-[3px_3px_0px_#5D534B] hover:shadow-[1px_1px_0px_#5D534B] transition-all disabled:opacity-50 ${a.payment_status==="paid"?"bg-red-100 text-red-800 hover:bg-red-200":"bg-green-100 text-green-800 hover:bg-green-200"}`,children:d===a.id?"Memproses...":a.payment_status==="paid"?"Tandai Belum Lunas":"Tandai Lunas"})})]})},a.id))}),Te=()=>{const{members:t,loading:n,currentPage:l,totalPages:d,itemsPerPage:r,totalMembers:o,addMember:x,updateMember:b,deleteMember:h,toggleMemberStatus:m,setPage:c,searchMembers:a,filterMembers:s}=ae(),{isMobile:i}=te(),[g,N]=p.useState(null),[C,M]=p.useState({}),[U,k]=p.useState(!1),[S,A]=p.useState(null),[F,v]=p.useState(!1),E=u=>{N(u.id),M({...u,payment_date:u.payment_date||void 0})},R=(u,f)=>{M(Q=>({...Q,[u]:f}))},W=u=>{$(u.id)},$=async u=>{if(confirm("Apakah Anda yakin ingin menghapus anggota ini?"))try{v(!0),await h(u),y.success("Data anggota berhasil dihapus")}catch(f){B(f,"AdminMembersPage (admin)"),y.error("Gagal menghapus data anggota")}finally{v(!1)}},X=async u=>{try{v(!0),await ne(()=>x(u),3,1e3,"Add Member"),k(!1),y.success("✅ Anggota berhasil ditambahkan ke database")}catch(f){throw B(f,"Add Member"),f}finally{v(!1)}},z=async u=>{try{A(u.id),await m(u.id),y.success("Status pembayaran berhasil diperbarui di database")}catch(f){B(f,"AdminMembersPage (admin)"),y.error("Gagal memperbarui status pembayaran di database")}finally{A(null)}},L=async()=>{try{if(!g)return;v(!0),await b(g,C),N(null),y.success("Data anggota berhasil diperbarui di database")}catch(u){B(u,"AdminMembersPage (admin)"),y.error("Gagal memperbarui data anggota di database")}finally{v(!1)}};return n?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx(se,{size:"medium",variant:"secondary",text:"Memuat Data Anggota dari Database..."})}):e.jsxs(e.Fragment,{children:[i&&e.jsx(le,{title:"Kelola Anggota",showBackButton:!0}),e.jsxs("div",{className:`space-y-6 ${i?"pt-20 pb-24 px-4":""}`,children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h2",{className:"text-xl font-black",children:"Kelola Anggota"}),!i&&e.jsxs("button",{type:"button",className:"neo-button-green text-sm flex items-center",onClick:()=>k(!0),children:[e.jsx(H,{size:16,className:"mr-2"}),"Tambah Anggota"]})]}),i&&e.jsxs("button",{type:"button",onClick:()=>k(!0),className:"w-full flex items-center justify-center px-4 py-3 bg-[#B39DDB] text-white rounded-lg hover:bg-[#A389D1] transition-colors font-medium tap-target",children:[e.jsx(H,{size:20,className:"mr-2"}),"Tambah Anggota Baru"]}),e.jsx(fe,{totalMembers:o,onSearch:a,onFilter:s}),U&&e.jsx(be,{onSubmit:X,onCancel:()=>k(!1),loading:F}),i?e.jsx(je,{members:t,editingId:g,editValues:C,toggleLoading:S,actionLoading:F,onEdit:E,onDelete:W,onToggleStatus:z,onSaveEdit:L,onCancelEdit:()=>N(null),onChange:R}):e.jsx(ye,{members:t,editingId:g,editValues:C,toggleLoading:S,actionLoading:F,onEdit:E,onDelete:$,onToggleStatus:z,onSaveEdit:L,onCancelEdit:()=>N(null),onChange:R}),e.jsx(xe,{currentPage:l,totalPages:d,totalItems:o,itemsPerPage:r,onPageChange:c})]})]})};export{Te as default};
