import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Trash2,
  Plus,
  Save,
  X,
  TrendingDown,
  Calendar,
  Tag,
  FileText,
  Search,
  BarChart3,
  Edit3
} from 'lucide-react';

import { formatRupiah, formatDate } from '../../utils/formatters';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { Card, CardContent } from '../../components/ui/card';
import { toast } from 'sonner';
import Loader from '../../components/Loader';
import { useExpensesContext } from '../../context/ExpensesContext';
import { handleError, withRetry } from '../../utils/errorHandler';

// Types
interface NewExpense {
  description: string;
  amount: number;
  date: string;
  category: string;
}

// Predefined categories
const EXPENSE_CATEGORIES = [
  'Operasional',
  'Konsumsi',
  'Transport',
  'Sound System',
  'Lainnya'
];

const AdminExpensesPage: React.FC = () => {
  // Context
  const { expenses, loading, addExpense, updateExpense, deleteExpense } = useExpensesContext();

  // Helper functions for currency formatting
  const formatCurrencyInput = (value: number): string => {
    if (!value || value === 0) return '';
    return new Intl.NumberFormat('id-ID').format(value);
  };

  const parseCurrencyInput = (value: string): number => {
    if (!value) return 0;
    // Remove all non-digit characters except decimal point
    const cleanValue = value.replace(/[^\d]/g, '');
    return parseInt(cleanValue) || 0;
  };


  const [isAdding, setIsAdding] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [newExpense, setNewExpense] = useState<NewExpense>({
    description: '',
    amount: 0,
    date: new Date().toISOString().split('T')[0],
    category: ''
  });
  const [editExpense, setEditExpense] = useState<NewExpense>({
    description: '',
    amount: 0,
    date: new Date().toISOString().split('T')[0],
    category: ''
  });
  const [actionLoading, setActionLoading] = useState(false);


  const filteredExpenses = useMemo(() => {
    return expenses.filter(expense => {
      const matchesSearch = expense.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           expense.category?.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = !selectedCategory || expense.category === selectedCategory;
      return matchesSearch && matchesCategory;
    });
  }, [expenses, searchTerm, selectedCategory]);

  // Calculate total
  const totalExpenses = useMemo(() => {
    return filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0);
  }, [filteredExpenses]);

  // Event Handlers
  const handleAdd = async () => {
    try {
      setActionLoading(true);

      await withRetry(
        () => addExpense({
          description: newExpense.description,
          amount: newExpense.amount,
          date: newExpense.date,
          category: newExpense.category,
          type: 'expense',
          createdBy: 'admin'
        }),
        3,
        1000,
        'Add Expense'
      );

      toast.success('✅ Pengeluaran berhasil ditambahkan');

      // Reset form
      setNewExpense({
        description: '',
        amount: 0,
        date: new Date().toISOString().split('T')[0],
        category: ''
      });
      setIsAdding(false);
    } catch (error) {
      handleError(error, 'Add Expense');
    } finally {
      setActionLoading(false);
    }
  };

  const handleEdit = (expense: { id: string; description: string; amount: number; date: string; category?: string }) => {
    setEditExpense({
      description: expense.description,
      amount: expense.amount,
      date: expense.date,
      category: expense.category || ''
    });
    setEditingId(expense.id);
    setIsEditing(true);
  };

  const handleUpdateExpense = async () => {
    if (!editingId) return;

    try {
      setActionLoading(true);

      await withRetry(
        () => updateExpense(editingId, {
          description: editExpense.description,
          amount: editExpense.amount,
          date: editExpense.date,
          category: editExpense.category
        }),
        3,
        1000,
        'Update Expense'
      );

      toast.success('✅ Pengeluaran berhasil diperbarui');

      // Reset form
      setEditExpense({
        description: '',
        amount: 0,
        date: new Date().toISOString().split('T')[0],
        category: ''
      });
      setIsEditing(false);
      setEditingId(null);
    } catch (error) {
      handleError(error, 'Update Expense');
    } finally {
      setActionLoading(false);
    }
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditingId(null);
    setEditExpense({
      description: '',
      amount: 0,
      date: new Date().toISOString().split('T')[0],
      category: ''
    });
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Yakin ingin menghapus pengeluaran ini?')) return;

    try {
      setActionLoading(true);
      await deleteExpense(id);
      toast.success('✅ Pengeluaran berhasil dihapus');
    } catch (error) {
      handleError(error, 'Delete Expense');
    } finally {
      setActionLoading(false);
    }
  };



  // Loading State
  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader size="medium" text="Memuat Data Pengeluaran..." />
      </div>
    );
  }

  return (
    <div className="space-y-6 p-4 sm:p-6">
      {/* Header Section */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-3"
      >
        <div className="p-3 bg-gradient-to-br from-red-100 to-orange-100 rounded-xl">
          <TrendingDown className="w-8 h-8 text-red-600" />
        </div>
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-[#5D534B]">Kelola Pengeluaran</h1>
          <p className="text-[#5D534B] opacity-70 text-sm sm:text-base">Pantau dan kelola semua pengeluaran organisasi</p>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6"
      >
        <Card className="neo-card bg-[#FF9898] border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B]">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-[#5D534B] text-sm font-medium">Total Pengeluaran</p>
                <p className="text-2xl font-bold text-[#5D534B]">{formatRupiah(totalExpenses)}</p>
              </div>
              <div className="p-3 bg-[#5D534B] border-2 border-[#5D534B] shadow-[2px_2px_0px_rgba(0,0,0,0.3)]">
                <div className="w-6 h-6 flex items-center justify-center">
                  <span className="text-white font-bold text-sm">Rp</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="neo-card bg-[#B39DDB] border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B]">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-[#5D534B] text-sm font-medium">Jumlah Transaksi</p>
                <p className="text-2xl font-bold text-[#5D534B]">{filteredExpenses.length}</p>
              </div>
              <div className="p-3 bg-[#5D534B] border-2 border-[#5D534B] shadow-[2px_2px_0px_rgba(0,0,0,0.3)]">
                <BarChart3 className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Add Expense Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.15 }}
        className="flex justify-center"
      >
        <Button
          type="button"
          onClick={() => setIsAdding(true)}
          className="bg-[#FF9898] text-[#5D534B] px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base font-bold flex items-center space-x-2 border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B] hover:shadow-[2px_2px_0px_#5D534B] hover:bg-[#FF8A8A] transition-all duration-200 rounded-lg"
          disabled={isAdding}
        >
          <Plus size={18} className="sm:w-5 sm:h-5" />
          <span className="hidden sm:inline">Tambah Pengeluaran Baru</span>
          <span className="sm:hidden">Tambah Pengeluaran</span>
        </Button>
      </motion.div>

      {/* Search and Filter */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="neo-card border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B]">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#5D534B] w-5 h-5" />
                  <Input
                    placeholder="Cari pengeluaran..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 h-12 border-2 border-[#5D534B] focus:border-[#FF9898] focus:ring-[#FF9898] shadow-[2px_2px_0px_#5D534B] font-medium"
                  />
                </div>
              </div>
              <div className="sm:w-48">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full h-12 px-3 border-2 border-[#5D534B] rounded-lg focus:border-[#FF9898] focus:ring-[#FF9898] bg-white shadow-[2px_2px_0px_#5D534B] font-medium"
                  title="Filter berdasarkan kategori"
                  aria-label="Filter berdasarkan kategori"
                >
                  <option value="">Semua Kategori</option>
                  {EXPENSE_CATEGORIES.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Add Expense Form */}
      <AnimatePresence>
        {isAdding && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="neo-card bg-gradient-to-br from-red-50 to-orange-50 border-red-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-red-100 rounded-lg">
                      <Plus className="w-6 h-6 text-red-600" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">Tambah Pengeluaran Baru</h3>
                  </div>
                  <Button
                    type="button"
                    onClick={() => setIsAdding(false)}
                    className="p-2 hover:bg-red-100 rounded-lg transition-colors"
                    variant="ghost"
                  >
                    <X className="w-5 h-5 text-gray-500" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="description" className="flex items-center space-x-2 text-sm font-semibold text-gray-700 mb-2">
                        <FileText className="w-4 h-4" />
                        <span>Deskripsi Pengeluaran</span>
                      </label>
                      <Input
                        id="description"
                        value={newExpense.description}
                        onChange={(e) => setNewExpense({ ...newExpense, description: e.target.value })}
                        className="h-12 border-gray-300 focus:border-red-500 focus:ring-red-500"
                        placeholder="Contoh: Pembelian ATK untuk acara"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="amount" className="flex items-center space-x-2 text-sm font-semibold text-gray-700 mb-2">
                        <div className="w-4 h-4 flex items-center justify-center">
                          <span className="text-gray-700 font-bold text-xs">Rp</span>
                        </div>
                        <span>Jumlah Pengeluaran</span>
                      </label>
                      <Input
                        id="amount"
                        type="text"
                        value={formatCurrencyInput(newExpense.amount)}
                        onChange={(e) => {
                          const numericValue = parseCurrencyInput(e.target.value);
                          setNewExpense({ ...newExpense, amount: numericValue });
                        }}
                        className="h-12 border-gray-300 focus:border-red-500 focus:ring-red-500"
                        placeholder="Contoh: 50.000"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label htmlFor="date" className="flex items-center space-x-2 text-sm font-semibold text-gray-700 mb-2">
                        <Calendar className="w-4 h-4" />
                        <span>Tanggal Pengeluaran</span>
                      </label>
                      <Input
                        id="date"
                        type="date"
                        value={newExpense.date}
                        onChange={(e) => setNewExpense({ ...newExpense, date: e.target.value })}
                        className="h-12 border-gray-300 focus:border-red-500 focus:ring-red-500"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="category" className="flex items-center space-x-2 text-sm font-semibold text-gray-700 mb-2">
                        <Tag className="w-4 h-4" />
                        <span>Kategori</span>
                      </label>
                      <select
                        id="category"
                        value={newExpense.category}
                        onChange={(e) => setNewExpense({ ...newExpense, category: e.target.value })}
                        className="w-full h-12 px-3 border border-gray-300 rounded-lg focus:border-red-500 focus:ring-red-500 bg-white"
                      >
                        <option value="">Pilih kategori</option>
                        {EXPENSE_CATEGORIES.map(category => (
                          <option key={category} value={category}>{category}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 mt-8">
                  <Button
                    type="button"
                    onClick={() => setIsAdding(false)}
                    className="px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-semibold transition-colors"
                    variant="ghost"
                  >
                    Batal
                  </Button>
                  <Button
                    type="button"
                    onClick={handleAdd}
                    disabled={!newExpense.description || newExpense.amount <= 0 || !newExpense.date || actionLoading}
                    className="px-6 py-3 bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {actionLoading ? (
                      <>
                        <Save size={18} className="mr-2 animate-spin" />
                        Menyimpan...
                      </>
                    ) : (
                      <>
                        <Save size={18} className="mr-2" />
                        Simpan Pengeluaran
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Edit Expense Form */}
      <AnimatePresence>
        {isEditing && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="neo-card bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Edit3 className="w-6 h-6 text-blue-600" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">Edit Pengeluaran</h3>
                  </div>
                  <Button
                    type="button"
                    onClick={handleCancelEdit}
                    className="p-2 hover:bg-blue-100 rounded-lg transition-colors"
                    variant="ghost"
                  >
                    <X className="w-5 h-5 text-gray-500" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="edit-description" className="flex items-center space-x-2 text-sm font-semibold text-gray-700 mb-2">
                        <FileText className="w-4 h-4" />
                        <span>Deskripsi Pengeluaran</span>
                      </label>
                      <Input
                        id="edit-description"
                        value={editExpense.description}
                        onChange={(e) => setEditExpense({ ...editExpense, description: e.target.value })}
                        className="h-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        placeholder="Contoh: Pembelian ATK untuk acara"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="edit-amount" className="flex items-center space-x-2 text-sm font-semibold text-gray-700 mb-2">
                        <div className="w-4 h-4 flex items-center justify-center">
                          <span className="text-gray-700 font-bold text-xs">Rp</span>
                        </div>
                        <span>Jumlah Pengeluaran</span>
                      </label>
                      <Input
                        id="edit-amount"
                        type="text"
                        value={formatCurrencyInput(editExpense.amount)}
                        onChange={(e) => {
                          const numericValue = parseCurrencyInput(e.target.value);
                          setEditExpense({ ...editExpense, amount: numericValue });
                        }}
                        className="h-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        placeholder="Contoh: 50.000"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label htmlFor="edit-date" className="flex items-center space-x-2 text-sm font-semibold text-gray-700 mb-2">
                        <Calendar className="w-4 h-4" />
                        <span>Tanggal Pengeluaran</span>
                      </label>
                      <Input
                        id="edit-date"
                        type="date"
                        value={editExpense.date}
                        onChange={(e) => setEditExpense({ ...editExpense, date: e.target.value })}
                        className="h-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="edit-category" className="flex items-center space-x-2 text-sm font-semibold text-gray-700 mb-2">
                        <Tag className="w-4 h-4" />
                        <span>Kategori</span>
                      </label>
                      <select
                        id="edit-category"
                        value={editExpense.category}
                        onChange={(e) => setEditExpense({ ...editExpense, category: e.target.value })}
                        className="w-full h-12 px-3 border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-blue-500 bg-white"
                      >
                        <option value="">Pilih kategori</option>
                        {EXPENSE_CATEGORIES.map(category => (
                          <option key={category} value={category}>{category}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 mt-8">
                  <Button
                    type="button"
                    onClick={handleCancelEdit}
                    className="px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-semibold transition-colors"
                    variant="ghost"
                  >
                    Batal
                  </Button>
                  <Button
                    type="button"
                    onClick={handleUpdateExpense}
                    disabled={!editExpense.description || editExpense.amount <= 0 || !editExpense.date || actionLoading}
                    className="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {actionLoading ? (
                      <>
                        <Save size={18} className="mr-2 animate-spin" />
                        Memperbarui...
                      </>
                    ) : (
                      <>
                        <Save size={18} className="mr-2" />
                        Perbarui Pengeluaran
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Expenses List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        {filteredExpenses.length === 0 ? (
          <Card className="neo-card">
            <CardContent className="p-12 text-center">
              <div className="flex flex-col items-center space-y-4">
                <div className="p-4 bg-gray-100 rounded-full">
                  <TrendingDown className="w-12 h-12 text-gray-400" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {searchTerm || selectedCategory ? 'Tidak ada hasil' : 'Belum ada pengeluaran'}
                  </h3>
                  <p className="text-gray-600">
                    {searchTerm || selectedCategory
                      ? 'Coba ubah filter pencarian Anda'
                      : 'Tambah pengeluaran pertama untuk mulai melacak keuangan'
                    }
                  </p>
                </div>
                {!searchTerm && !selectedCategory && (
                  <Button
                    type="button"
                    onClick={() => setIsAdding(true)}
                    className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white px-6 py-3 rounded-xl font-semibold"
                  >
                    <Plus size={18} className="mr-2" />
                    Tambah Pengeluaran Pertama
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {/* Desktop Table */}
            <div className="hidden lg:block">
              <Card className="neo-card border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B] overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-[#FF9898] border-b-2 border-[#5D534B]">
                      <tr>
                        <th className="text-left py-4 px-6 font-bold text-[#5D534B]">Deskripsi</th>
                        <th className="text-left py-4 px-6 font-bold text-[#5D534B]">Tanggal</th>
                        <th className="text-left py-4 px-6 font-bold text-[#5D534B]">Kategori</th>
                        <th className="text-right py-4 px-6 font-bold text-[#5D534B]">Jumlah</th>
                        <th className="text-center py-4 px-6 font-bold text-[#5D534B]">Aksi</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredExpenses.map((expense, index) => (
                        <motion.tr
                          key={expense.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.05 }}
                          className="border-b-2 border-[#5D534B] hover:bg-[#FF9898]/20 transition-colors"
                        >
                          <td className="py-4 px-6">
                            <div className="flex items-center space-x-3">
                              <div className="p-2 bg-[#FF9898] border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] rounded-lg">
                                <FileText className="w-4 h-4 text-[#5D534B]" />
                              </div>
                              <span className="font-medium text-[#5D534B]">{expense.description}</span>
                            </div>
                          </td>
                          <td className="py-4 px-6 text-[#5D534B] font-medium">{formatDate(expense.date)}</td>
                          <td className="py-4 px-6">
                            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#B39DDB] text-[#5D534B] border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B]">
                              {expense.category || 'Lainnya'}
                            </span>
                          </td>
                          <td className="py-4 px-6 text-right">
                            <span className="text-lg font-bold text-[#5D534B]">{formatRupiah(expense.amount)}</span>
                          </td>
                          <td className="py-4 px-6 text-center">
                            <div className="flex items-center justify-center space-x-2">
                              <Button
                                type="button"
                                onClick={() => handleEdit(expense)}
                                className="p-2 bg-blue-100 hover:bg-blue-200 text-blue-600 border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[1px_1px_0px_#5D534B] rounded-lg transition-all"
                                disabled={actionLoading}
                                title="Edit pengeluaran"
                                variant="ghost"
                              >
                                <Edit3 size={16} />
                              </Button>
                              <Button
                                type="button"
                                onClick={() => handleDelete(expense.id)}
                                className="p-2 bg-red-100 hover:bg-red-200 text-red-600 border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[1px_1px_0px_#5D534B] rounded-lg transition-all"
                                disabled={actionLoading}
                                title="Hapus pengeluaran"
                                variant="ghost"
                              >
                                <Trash2 size={16} />
                              </Button>
                            </div>
                          </td>
                        </motion.tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </Card>
            </div>

            {/* Mobile Cards */}
            <div className="lg:hidden space-y-4">
              {filteredExpenses.map((expense, index) => (
                <motion.div
                  key={expense.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="neo-card border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B] hover:shadow-[2px_2px_0px_#5D534B] transition-all">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3 flex-1">
                          <div className="p-2 bg-[#FF9898] border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] rounded-lg">
                            <FileText className="w-5 h-5 text-[#5D534B]" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="font-medium text-[#5D534B] truncate">{expense.description}</h3>
                            <p className="text-sm text-[#5D534B] font-medium">{formatDate(expense.date)}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 ml-2">
                          <Button
                            type="button"
                            onClick={() => handleEdit(expense)}
                            className="p-2 bg-blue-100 hover:bg-blue-200 text-blue-600 border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[1px_1px_0px_#5D534B] rounded-lg transition-all"
                            disabled={actionLoading}
                            title="Edit pengeluaran"
                            variant="ghost"
                          >
                            <Edit3 size={16} />
                          </Button>
                          <Button
                            type="button"
                            onClick={() => handleDelete(expense.id)}
                            className="p-2 bg-red-100 hover:bg-red-200 text-red-600 border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[1px_1px_0px_#5D534B] rounded-lg transition-all"
                            disabled={actionLoading}
                            title="Hapus pengeluaran"
                            variant="ghost"
                          >
                            <Trash2 size={16} />
                          </Button>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#B39DDB] text-[#5D534B] border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B]">
                          {expense.category || 'Lainnya'}
                        </span>
                        <span className="text-lg font-bold text-[#5D534B]">{formatRupiah(expense.amount)}</span>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        )}
      </motion.div>
    </div>
  );
};

export default AdminExpensesPage;
