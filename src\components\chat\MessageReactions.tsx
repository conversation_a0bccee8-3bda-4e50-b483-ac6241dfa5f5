import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, X } from 'lucide-react';
import { useChatContext } from '../../context/ChatContext';
import { useAuth } from '../../context/AuthContext';
import { Message, MessageReaction } from '../../types/chat';

interface MessageReactionsProps {
  message: Message;
  isVisible: boolean;
  onClose: () => void;
}

const MessageReactions: React.FC<MessageReactionsProps> = ({ 
  message, 
  isVisible, 
  onClose 
}) => {
  const { addReaction, removeReaction } = useChatContext();
  const { user } = useAuth();
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);

  const commonEmojis = ['👍', '❤️', '😂', '😮', '😢', '😡', '🎉', '🔥'];

  const handleReactionClick = async (emoji: string) => {
    if (!user) return;

    const existingReaction = message.reactions.find(
      r => r.userId === user.uid && r.emoji === emoji
    );

    if (existingReaction) {
      await removeReaction(message.id, emoji);
    } else {
      await addReaction(message.id, emoji);
    }
  };

  const getReactionCount = (emoji: string) => {
    return message.reactions.filter(r => r.emoji === emoji).length;
  };

  const hasUserReacted = (emoji: string) => {
    return message.reactions.some(r => r.userId === user?.uid && r.emoji === emoji);
  };

  // Group reactions by emoji
  const groupedReactions = message.reactions.reduce((acc, reaction) => {
    if (!acc[reaction.emoji]) {
      acc[reaction.emoji] = [];
    }
    acc[reaction.emoji].push(reaction);
    return acc;
  }, {} as Record<string, MessageReaction[]>);

  return (
    <>
      {/* Existing Reactions */}
      {Object.keys(groupedReactions).length > 0 && (
        <div className="flex flex-wrap gap-1 mt-2">
          {Object.entries(groupedReactions).map(([emoji, reactions]) => (
            <motion.button
              key={emoji}
              onClick={() => handleReactionClick(emoji)}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className={`flex items-center space-x-1 px-2 py-1 text-xs border-2 border-[#5D534B] rounded-full transition-all ${
                hasUserReacted(emoji)
                  ? 'bg-[#FCE09B] shadow-[2px_2px_0px_#5D534B]'
                  : 'bg-white shadow-[1px_1px_0px_#5D534B] hover:shadow-[2px_2px_0px_#5D534B]'
              }`}
            >
              <span>{emoji}</span>
              <span className="font-bold text-[#5D534B]">{reactions.length}</span>
            </motion.button>
          ))}
        </div>
      )}

      {/* Reaction Picker */}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 10 }}
            className="absolute top-full left-0 mt-2 z-10"
          >
            <div className="bg-white border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B] rounded-lg p-3">
              {/* Quick Reactions */}
              <div className="flex items-center space-x-2 mb-3">
                {commonEmojis.map((emoji) => (
                  <motion.button
                    key={emoji}
                    onClick={() => {
                      handleReactionClick(emoji);
                      onClose();
                    }}
                    whileHover={{ scale: 1.2 }}
                    whileTap={{ scale: 0.9 }}
                    className={`w-8 h-8 flex items-center justify-center border-2 border-[#5D534B] rounded-full transition-all ${
                      hasUserReacted(emoji)
                        ? 'bg-[#FCE09B] shadow-[2px_2px_0px_#5D534B]'
                        : 'bg-white hover:bg-[#FCE09B]/20 shadow-[1px_1px_0px_#5D534B]'
                    }`}
                  >
                    {emoji}
                  </motion.button>
                ))}
                
                {/* More Emojis Button */}
                <button
                  onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                  title="More reactions"
                  aria-label="More reactions"
                  className="w-8 h-8 flex items-center justify-center bg-[#F9F9F9] border-2 border-[#5D534B] shadow-[1px_1px_0px_#5D534B] hover:shadow-[2px_2px_0px_#5D534B] transition-all rounded-full"
                >
                  <Plus className="w-4 h-4 text-[#5D534B]" />
                </button>
              </div>

              {/* Extended Emoji Picker */}
              {showEmojiPicker && (
                <div className="border-t-2 border-[#5D534B] pt-3">
                  <div className="grid grid-cols-6 gap-2 max-w-48">
                    {[
                      '😀', '😃', '😄', '😁', '😆', '😅',
                      '😂', '🤣', '😊', '😇', '🙂', '🙃',
                      '😉', '😌', '😍', '🥰', '😘', '😗',
                      '😙', '😚', '😋', '😛', '😝', '😜',
                      '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
                      '🥳', '😏', '😒', '😞', '😔', '😟',
                      '😕', '🙁', '☹️', '😣', '😖', '😫',
                      '😩', '🥺', '😢', '😭', '😤', '😠',
                      '😡', '🤬', '🤯', '😳', '🥵', '🥶',
                      '😱', '😨', '😰', '😥', '😓', '🤗',
                      '🤔', '🤭', '🤫', '🤥', '😶', '😐',
                      '😑', '😬', '🙄', '😯', '😦', '😧',
                      '😮', '😲', '🥱', '😴', '🤤', '😪',
                      '😵', '🤐', '🥴', '🤢', '🤮', '🤧',
                      '😷', '🤒', '🤕', '🤑', '🤠', '😈',
                      '👿', '👹', '👺', '🤡', '💩', '👻',
                      '💀', '☠️', '👽', '👾', '🤖', '🎃'
                    ].map((emoji) => (
                      <button
                        key={emoji}
                        onClick={() => {
                          handleReactionClick(emoji);
                          onClose();
                        }}
                        className="w-6 h-6 flex items-center justify-center hover:bg-[#FCE09B]/20 rounded transition-colors"
                      >
                        {emoji}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Close Button */}
              <button
                onClick={onClose}
                title="Close reactions"
                aria-label="Close reactions"
                className="absolute -top-2 -right-2 w-6 h-6 bg-[#FF9898] border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] rounded-full flex items-center justify-center"
              >
                <X className="w-3 h-3 text-[#5D534B]" />
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default MessageReactions;
