#!/bin/bash

# <PERSON><PERSON><PERSON>EMUDA Android Build Script
# This script automates the process of building the Android app

echo "🚀 Building DANAPEMUDA Android App..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

# Clean previous builds
print_status "Cleaning previous builds..."
rm -rf dist/
rm -rf android/app/build/

# Install dependencies
print_status "Installing dependencies..."
npm ci

if [ $? -ne 0 ]; then
    print_error "Failed to install dependencies"
    exit 1
fi

# Build web app
print_status "Building web app..."
npm run build:mobile

if [ $? -ne 0 ]; then
    print_error "Failed to build web app"
    exit 1
fi

# Sync with Capacitor
print_status "Syncing with Capacitor..."
npx cap sync android

if [ $? -ne 0 ]; then
    print_error "Failed to sync with Capacitor"
    exit 1
fi

# Copy assets
print_status "Copying assets..."
npx cap copy android

if [ $? -ne 0 ]; then
    print_error "Failed to copy assets"
    exit 1
fi

print_success "Build completed successfully!"
print_status "Next steps:"
echo "  1. Open Android Studio: npm run android:open"
echo "  2. Build APK in Android Studio"
echo "  3. Or run on device: npm run android:dev"

print_warning "Make sure you have Android Studio installed and configured."
