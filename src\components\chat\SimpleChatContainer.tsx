import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, MessageSquare, Send, Image } from 'lucide-react';
import { useSimpleChatContext } from '../../context/SimpleChatContext';
import NameInputModal from './NameInputModal';

interface SimpleChatContainerProps {
  isOpen: boolean;
  onClose: () => void;
}

const SimpleChatContainer: React.FC<SimpleChatContainerProps> = ({ isOpen, onClose }) => {
  const { 
    currentRoom, 
    messages, 
    isLoading, 
    currentUser, 
    showNameInput, 
    sendMessage, 
    uploadImage, 
    setUserName 
  } = useSimpleChatContext();
  
  const [messageInput, setMessageInput] = useState('');
  const [isSending, setIsSending] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!messageInput.trim() || isSending) return;

    setIsSending(true);
    try {
      await sendMessage(messageInput);
      setMessageInput('');
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsSending(false);
    }
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      await uploadImage(file);
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Hanya file gambar yang diperbolehkan!');
    }
    
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('id-ID', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ x: '100%' }}
        animate={{ x: 0 }}
        exit={{ x: '100%' }}
        transition={{ type: 'tween', duration: 0.3 }}
        className="fixed inset-0 bg-[#F9F9F9] z-50 flex flex-col"
      >
        {/* Header */}
        <div className="bg-[#FCE09B] border-b-4 border-[#5D534B] p-4 flex items-center justify-between safe-area-pt">
          <div className="flex items-center space-x-4">
            <button
              type="button"
              onClick={onClose}
              title="Tutup Chat"
              aria-label="Tutup Chat"
              className="w-10 h-10 bg-[#FF9898] border-3 border-[#5D534B] shadow-[3px_3px_0px_#5D534B] hover:shadow-[5px_5px_0px_#5D534B] transition-all rounded-full flex items-center justify-center"
            >
              <X className="w-5 h-5 text-[#5D534B] font-bold" />
            </button>
            <MessageSquare className="w-6 h-6 text-[#5D534B]" />
            <div>
              <h2 className="text-xl font-black text-[#5D534B]">
                {currentRoom?.name || 'DANAPEMUDA Chat'}
              </h2>
              {currentUser && (
                <p className="text-sm text-[#5D534B]/70">
                  Sebagai: {currentUser.name}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {isLoading ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin w-8 h-8 border-4 border-[#5D534B] border-t-transparent rounded-full mx-auto mb-4"></div>
                <p className="text-[#5D534B] font-bold">Memuat chat...</p>
              </div>
            </div>
          ) : (
            <>
              {/* Messages Area */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.length === 0 ? (
                  <div className="text-center py-8">
                    <MessageSquare className="w-16 h-16 text-[#5D534B]/30 mx-auto mb-4" />
                    <p className="text-[#5D534B]/60 font-medium">
                      Belum ada pesan. Mulai percakapan!
                    </p>
                  </div>
                ) : (
                  messages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`flex flex-col ${
                        message.senderId === currentUser?.id ? 'items-end' : 'items-start'
                      } mb-4`}
                    >
                      {/* Nama Pengirim */}
                      <p className={`text-xs font-bold text-[#5D534B] mb-1 px-1 ${
                        message.senderId === currentUser?.id ? 'text-right' : 'text-left'
                      }`}>
                        {message.senderId === currentUser?.id ? 'Anda' : message.senderName}
                      </p>

                      {/* Message Bubble */}
                      <div
                        className={`max-w-[80%] p-3 border-2 border-[#5D534B] rounded-lg ${
                          message.senderId === currentUser?.id
                            ? 'bg-[#FCE09B] shadow-[2px_2px_0px_#5D534B]'
                            : 'bg-white shadow-[2px_2px_0px_#5D534B]'
                        }`}
                      >
                        <p className="text-[#5D534B] font-medium break-words">
                          {message.content}
                        </p>
                        <p className="text-xs text-[#5D534B]/60 mt-1">
                          {formatTime(message.timestamp)}
                        </p>
                      </div>
                    </motion.div>
                  ))
                )}
              </div>

              {/* Input Area */}
              <div className="border-t-4 border-[#5D534B] bg-white p-4 safe-area-pb">
                <form onSubmit={handleSendMessage} className="flex items-end space-x-2">
                  <div className="flex-1">
                    <textarea
                      value={messageInput}
                      onChange={(e) => setMessageInput(e.target.value)}
                      placeholder="Ketik pesan..."
                      className="w-full p-3 border-2 border-[#5D534B] rounded-lg bg-[#F9F9F9] text-[#5D534B] placeholder-[#5D534B]/60 font-medium focus:outline-none focus:shadow-[2px_2px_0px_#5D534B] transition-all resize-none min-h-[48px] max-h-[120px]"
                      rows={1}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          handleSendMessage(e);
                        }
                      }}
                    />
                  </div>
                  
                  {/* Image Upload Button */}
                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    title="Kirim Gambar"
                    aria-label="Kirim Gambar"
                    className="p-3 bg-[#9DE0D2] border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[4px_4px_0px_#5D534B] transition-all rounded-lg"
                  >
                    <Image className="w-5 h-5 text-[#5D534B]" />
                  </button>
                  
                  {/* Send Button */}
                  <button
                    type="submit"
                    disabled={!messageInput.trim() || isSending}
                    className="p-3 bg-[#FCE09B] border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[4px_4px_0px_#5D534B] disabled:opacity-50 disabled:cursor-not-allowed transition-all rounded-lg"
                  >
                    {isSending ? (
                      <div className="animate-spin w-5 h-5 border-2 border-[#5D534B] border-t-transparent rounded-full"></div>
                    ) : (
                      <Send className="w-5 h-5 text-[#5D534B]" />
                    )}
                  </button>
                </form>
                
                {/* Hidden File Input */}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  aria-label="Upload gambar"
                  title="Upload gambar"
                />
              </div>
            </>
          )}
        </div>
      </motion.div>

      {/* Name Input Modal */}
      <NameInputModal
        isOpen={showNameInput}
        onSubmit={setUserName}
      />
    </AnimatePresence>
  );
};

export default SimpleChatContainer;
