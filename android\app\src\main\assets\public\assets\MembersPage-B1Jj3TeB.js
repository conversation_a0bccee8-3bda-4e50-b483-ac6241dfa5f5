import{v as e,O as f,u as N,t as m,o as b,U as g}from"./index-Bh-WAD4O.js";import{f as D,a as v}from"./formatters-LGS2Cxr7.js";import{U as B,a as w}from"./user-x-D23CUg0I.js";function t({className:a,...r}){return e.jsx("div",{className:f("animate-pulse rounded-md bg-muted",a),...r})}const h=()=>e.jsx("div",{className:"neo-card neo-gradient-blue p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(t,{className:"h-4 w-24"}),e.jsx(t,{className:"h-8 w-32"})]}),e.jsx(t,{className:"h-12 w-12 rounded-full"})]})}),y=()=>e.jsxs("div",{className:"neo-card p-4 space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(t,{className:"h-6 w-40"}),e.jsx(t,{className:"h-6 w-16 rounded-full"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(t,{className:"h-4 w-32"}),e.jsx(t,{className:"h-4 w-24"})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(t,{className:"h-8 w-20"}),e.jsx(t,{className:"h-8 w-20"})]})]}),_=({title:a,description:r,action:l,icon:c})=>e.jsxs("div",{className:"neo-card p-8 text-center",children:[c&&e.jsx("div",{className:"flex justify-center mb-4",children:c}),e.jsx("h3",{className:"text-lg font-bold text-[#5D534B] mb-2",children:a}),e.jsx("p",{className:"text-[#5D534B] mb-4",children:r}),l&&l]}),E=()=>{const{members:a,loading:r}=N(),[l,c]=m.useState(null),[d,p]=m.useState("all"),[x,j]=m.useState("");m.useEffect(()=>{const s=a.filter(i=>i.payment_status==="paid").length,n=a.filter(i=>i.payment_status==="unpaid").length;c({totalMembers:a.length,paidMembers:s,unpaidMembers:n})},[a,d]);const o=s=>{p(s)},u=a.filter(s=>{const n=s.name.toLowerCase().includes(x.toLowerCase()),i=d==="all"||s.payment_status===d;return n&&i});return r?e.jsxs("div",{className:"bg-[#F9F9F9] text-[#5D534B]",children:[e.jsx("div",{className:"flex justify-between items-center mb-6",children:e.jsx("h1",{className:"text-2xl md:text-3xl font-bold border-b-4 border-[#9DE0D2] pb-2",children:"Anggota"})}),e.jsxs("div",{className:"grid grid-cols-3 gap-3 mb-4",children:[e.jsx(h,{}),e.jsx(h,{}),e.jsx(h,{})]}),e.jsxs("div",{className:"neo-card p-4",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"w-6 h-6 bg-gray-300 rounded mr-2 animate-pulse"}),e.jsx("div",{className:"h-6 w-32 bg-gray-300 rounded animate-pulse"})]}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:Array.from({length:8}).map((s,n)=>e.jsx(y,{},n))})]})]}):e.jsxs("div",{className:"bg-[#F9F9F9] text-[#5D534B]",children:[e.jsx("div",{className:"flex justify-between items-center mb-6",children:e.jsx("h1",{className:"text-2xl md:text-3xl font-bold border-b-4 border-[#9DE0D2] pb-2",children:"Anggota"})}),l&&e.jsxs("div",{className:"grid grid-cols-3 gap-3 mb-4",children:[e.jsxs("div",{className:"flex items-center p-2 bg-white border-2 border-[#5D534B] rounded-md shadow-[3px_3px_0px_#5D534B]",children:[e.jsx("div",{className:"p-1.5 rounded-full bg-[#FCE09B]/80 flex-shrink-0",children:e.jsx(b,{size:14,className:"text-[#5D534B]"})}),e.jsxs("div",{className:"ml-2 min-w-0",children:[e.jsx("div",{className:"text-xs text-[#5D534B]/70 truncate",children:"Total"}),e.jsx("div",{className:"text-base font-bold text-[#5D534B]",children:l.totalMembers})]})]}),e.jsxs("div",{className:"flex items-center p-2 bg-white border-2 border-[#5D534B] rounded-md shadow-[3px_3px_0px_#5D534B]",children:[e.jsx("div",{className:"p-1.5 rounded-full bg-[#9DE0D2]/80 flex-shrink-0",children:e.jsx(B,{size:14,className:"text-[#5D534B]"})}),e.jsxs("div",{className:"ml-2 min-w-0",children:[e.jsx("div",{className:"text-xs text-[#5D534B]/70 truncate",children:"Lunas"}),e.jsx("div",{className:"text-base font-bold text-[#5D534B]",children:l.paidMembers})]})]}),e.jsxs("div",{className:"flex items-center p-2 bg-white border-2 border-[#5D534B] rounded-md shadow-[3px_3px_0px_#5D534B]",children:[e.jsx("div",{className:"p-1.5 rounded-full bg-[#FF9898]/80 flex-shrink-0",children:e.jsx(w,{size:14,className:"text-[#5D534B]"})}),e.jsxs("div",{className:"ml-2 min-w-0",children:[e.jsx("div",{className:"text-xs text-[#5D534B]/70 truncate",children:"Belum"}),e.jsx("div",{className:"text-base font-bold text-[#5D534B]",children:l.unpaidMembers})]})]})]}),e.jsxs("div",{className:"neo-card p-4 overflow-x-auto animate-fade-in",children:[e.jsxs("h2",{className:"text-xl font-bold mb-4 flex items-center text-[#5D534B]",children:[e.jsx(b,{className:"mr-2",size:20}),"Daftar Anggota"]}),e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center mb-4 gap-3",children:[e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{type:"button",className:`px-3 py-1 text-sm border-4 border-[#5D534B] rounded-full ${d==="all"?"bg-[#9DE0D2] text-[#5D534B]":"bg-white text-[#5D534B]"}`,onClick:()=>o("all"),children:"Semua"}),e.jsx("button",{type:"button",className:`px-3 py-1 text-sm border-4 border-[#5D534B] rounded-full ${d==="paid"?"bg-[#9DE0D2] text-[#5D534B]":"bg-white text-[#5D534B]"}`,onClick:()=>o("paid"),children:"Lunas"}),e.jsx("button",{type:"button",className:`px-3 py-1 text-sm border-4 border-[#5D534B] rounded-full ${d==="unpaid"?"bg-[#FF9898] text-[#5D534B]":"bg-white text-[#5D534B]"}`,onClick:()=>o("unpaid"),children:"Belum Lunas"})]}),e.jsx("div",{className:"relative w-full md:w-64",children:e.jsx("input",{type:"text",placeholder:"Cari nama...",value:x,onChange:s=>j(s.target.value),className:"w-full border-4 border-[#5D534B] px-4 py-2 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#9DE0D2] focus:border-[#5D534B]"})})]}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:u.length>0?u.map(s=>e.jsxs("div",{className:"bg-white border-4 border-[#5D534B] rounded-xl shadow-[6px_6px_0px_#5D534B] overflow-hidden animate-fade-in",children:[e.jsxs("div",{className:"flex justify-between items-center p-3 border-b-2 border-[#5D534B]",children:[e.jsx("h3",{className:"text-md font-bold text-[#5D534B] truncate",children:s.name}),e.jsx("span",{className:`px-2 py-1 text-xs font-bold border-2 border-[#5D534B] rounded-full ${s.payment_status==="paid"?"bg-[#9DE0D2] text-[#5D534B]":"bg-[#FF9898] text-[#5D534B]"}`,children:s.payment_status==="paid"?"Lunas":"Belum Lunas"})]}),e.jsxs("div",{className:"p-3",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2 text-sm",children:[e.jsx("span",{className:"text-[#5D534B]",children:"Nominal Iuran:"}),e.jsx("span",{className:"font-bold text-[#5D534B]",children:D(s.payment_amount)})]}),s.payment_date&&e.jsxs("div",{className:"flex justify-between items-center text-sm",children:[e.jsx("span",{className:"text-[#5D534B]",children:"Tanggal Bayar:"}),e.jsx("span",{className:"font-bold text-[#5D534B]",children:v(s.payment_date)})]})]})]},s.id)):e.jsx("div",{className:"col-span-full",children:e.jsx(_,{title:"Tidak ada anggota ditemukan",description:x?`Tidak ada anggota dengan nama "${x}"`:"Belum ada data anggota",icon:e.jsx(g,{size:48,className:"text-[#5D534B]"})})})})]})]})};export{E as default};
