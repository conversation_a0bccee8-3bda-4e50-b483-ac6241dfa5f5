import{t as l,v as e,X as o}from"./index-DpPtfsbp.js";const c=({isOpen:s,onClose:t,title:d,children:a})=>{const n=l.useRef(null);return l.useEffect(()=>{const r=i=>{i.key==="Escape"&&t()};return s&&(document.addEventListener("keydown",r),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",r),document.body.style.overflow="unset"}},[s,t]),s?e.jsx("div",{className:"fixed inset-0 z-50 flex items-start justify-center p-4 pt-16 sm:pt-20 bg-black/50 overflow-y-auto",children:e.jsxs("div",{ref:n,className:"relative bg-white border-2 sm:border-4 border-[#5D534B] shadow-pastel w-full max-w-xl max-h-[calc(100vh-8rem)] sm:max-h-[calc(100vh-10rem)] rounded-xl sm:rounded-2xl flex flex-col animate-fade-in mb-8",children:[e.jsxs("div",{className:"sticky top-0 z-10 bg-white p-3 sm:p-4 border-b-2 sm:border-b-4 border-[#5D534B] flex justify-between items-center flex-shrink-0",children:[e.jsx("h2",{className:"text-lg sm:text-xl font-bold text-[#5D534B] break-words pr-2",children:d}),e.jsxs("button",{onClick:t,className:"border-2 sm:border-4 border-[#5D534B] p-1 hover:bg-[#FF9898] text-[#5D534B] transition-colors rounded-full flex-shrink-0","aria-label":"Tutup dialog",title:"Tutup",children:[e.jsx(o,{size:18,className:"sm:hidden"}),e.jsx(o,{size:20,className:"hidden sm:block"})]})]}),e.jsx("div",{className:"flex-1 overflow-y-auto p-4 sm:p-6 min-h-0",children:a})]})}):null};export{c as M};
