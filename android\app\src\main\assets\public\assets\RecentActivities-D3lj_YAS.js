import{s,x as m}from"./index-WP5euzF_.js";import{C as r,b as l,c as i,a as x}from"./card-B6Up8qcU.js";import{a as d}from"./formatters-LGS2Cxr7.js";const j=({activities:t,isLoading:c})=>c?s.jsxs(r,{className:"neo-card",children:[s.jsx(l,{children:s.jsx(i,{className:"text-[#5D534B] text-lg sm:text-xl",children:"Aktivitas Terbaru"})}),s.jsx(x,{children:s.jsx("div",{className:"space-y-3",children:Array.from({length:4}).map((e,a)=>s.jsx("div",{className:"animate-pulse",children:s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-8 h-8 bg-gray-200 rounded-full"}),s.jsxs("div",{className:"flex-1",children:[s.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-1"}),s.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]})},a))})})]}):s.jsxs(r,{className:"neo-card",children:[s.jsx(l,{children:s.jsx(i,{className:"text-[#5D534B] text-lg sm:text-xl",children:"Aktivitas Terbaru"})}),s.jsx(x,{children:s.jsx("div",{className:"space-y-3 sm:space-y-4",children:t.length===0?s.jsx("div",{className:"text-center py-8 text-gray-500",children:s.jsx("p",{className:"text-sm sm:text-base",children:"Belum ada aktivitas terbaru"})}):t.map((e,a)=>s.jsxs(m.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:a*.1},className:"flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-[#F3E5F5] flex items-center justify-center",children:s.jsx(e.icon,{size:16,className:"text-[#B39DDB] sm:w-5 sm:h-5"})})}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"text-sm sm:text-base text-[#5D534B] font-medium leading-tight",children:e.title}),s.jsx("p",{className:"text-xs sm:text-sm text-gray-500 mt-1",children:d(e.date)})]})]},`${e.type}-${a}`))})})]});export{j as default};
