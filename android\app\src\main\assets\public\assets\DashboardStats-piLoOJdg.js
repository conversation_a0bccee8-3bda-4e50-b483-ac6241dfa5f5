import{f as n,W as t,e as m,k as o,o as c}from"./index-BmrqMJCI.js";import{S as d}from"./StatCard-DjRJPQ_d.js";import{f as l}from"./formatters-LGS2Cxr7.js";import{C as g}from"./credit-card-DHCA50Hf.js";import{A as u}from"./activity-CLmoxnmS.js";const f=({stats:a,isLoading:i})=>{const s=[{title:"Total Anggota",value:a.totalMembers.toString(),icon:n,color:"#B39DDB",bgColor:"#F3E5F5"},{title:"Total Pemasukan",value:l(a.totalIncome),icon:t,color:"#4FC3F7",bgColor:"#E1F5FE"},{title:"Total Pengeluaran",value:l(a.totalExpense),icon:m,color:"#FF8A65",bgColor:"#FFF3E0"},{title:"Total Iuran",value:l(a.totalDues),icon:g,color:"#A1887F",bgColor:"#EFEBE9"},{title:"Saldo Bersih",value:l(a.netBalance),icon:u,color:a.netBalance>=0?"#66BB6A":"#EF5350",bgColor:a.netBalance>=0?"#E8F5E8":"#FFEBEE"}];return i?o.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8",children:Array.from({length:5}).map((e,r)=>o.jsx("div",{className:"bg-white p-4 sm:p-6 rounded-lg border border-gray-200 shadow-sm",children:o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsx("div",{className:"w-10 h-10 bg-gray-200 rounded-full animate-pulse"}),o.jsxs("div",{className:"flex-1",children:[o.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse"}),o.jsx("div",{className:"h-6 bg-gray-200 rounded w-1/2 animate-pulse"})]})]})},r))}):o.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8",children:s.map((e,r)=>o.jsx(c.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:r*.1},className:"w-full",children:o.jsx(d,{title:e.title,value:e.value,icon:e.icon,gradientClass:e.bgColor,borderColor:e.color})},e.title))})};export{f as default};
