# 📱 DEVICE SETUP GUIDE - DANAPEMUDA MOBILE

## 🔧 **SETUP ANDROID DEVICE**

### **Option 1: Physical Device (Recommended)**

#### **1. Enable Developer Options:**
1. Go to **Settings** > **About Phone**
2. Tap **Build Number** 7 times
3. Go back to **Settings** > **Developer Options**
4. Enable **USB Debugging**
5. Enable **Install via USB**

#### **2. Connect Device:**
1. Connect phone to computer via USB
2. Allow USB debugging when prompted
3. Select **File Transfer** mode

#### **3. Verify Connection:**
```bash
# Check if device is detected
adb devices
```

### **Option 2: Android Emulator**

#### **1. Install Android Studio:**
- Download from: https://developer.android.com/studio
- Install with default settings

#### **2. Create Virtual Device:**
1. Open Android Studio
2. Go to **Tools** > **AVD Manager**
3. Click **Create Virtual Device**
4. Choose **Phone** > **Pixel 4** (or similar)
5. Download **Android 11** (API 30) or higher
6. Click **Finish**

#### **3. Start Emulator:**
1. Click **Play** button in AVD Manager
2. Wait for emulator to boot completely

---

## 🚀 **RUNNING THE APP**

### **Method 1: Direct Run (Easiest)**
```bash
# This will build and run automatically
npm run android:dev
```

### **Method 2: Step by Step**
```bash
# 1. Build the web app
npm run build:mobile

# 2. Sync with Capacitor
npx cap sync android

# 3. Run on device/emulator
npx cap run android
```

### **Method 3: Using Android Studio**
```bash
# Open project in Android Studio
npm run android:open

# Then click "Run" button in Android Studio
```

---

## 🔍 **TROUBLESHOOTING**

### **Device Not Detected:**
```bash
# Check ADB connection
adb devices

# If empty, try:
adb kill-server
adb start-server
adb devices
```

### **USB Debugging Issues:**
1. Revoke USB debugging authorizations
2. Disconnect and reconnect device
3. Allow debugging when prompted

### **Build Errors:**
```bash
# Clean and rebuild
cd android
./gradlew clean
cd ..
npm run build:mobile
npx cap sync android
```

### **Emulator Issues:**
1. Ensure emulator is fully booted
2. Check if emulator appears in `adb devices`
3. Try restarting emulator

---

## ✅ **VERIFICATION STEPS**

### **1. Check Device Connection:**
```bash
adb devices
# Should show: device_id    device
```

### **2. Check Capacitor Status:**
```bash
npx cap doctor android
```

### **3. Test Build:**
```bash
npm run build:mobile
```

---

## 📱 **EXPECTED RESULTS**

When successful, you should see:
1. **App installs** on device/emulator
2. **DANAPEMUDA splash screen** appears
3. **App opens** with mobile-optimized UI
4. **Firebase connection** works
5. **All features** function properly

---

## 🎯 **QUICK START COMMANDS**

```bash
# For Physical Device:
1. Enable USB debugging on phone
2. Connect via USB
3. Run: npm run android:dev

# For Emulator:
1. Start Android Studio
2. Create/start emulator
3. Run: npm run android:dev
```

**Ready to test your mobile app! 📱✨**
