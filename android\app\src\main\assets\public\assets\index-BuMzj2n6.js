const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/web-Ci68E5o7.js","assets/index-DA4P39RP.js","assets/index-5z-ag9vX.css"])))=>i.map(i=>d[i]);
import{Q as t,_ as n}from"./index-DA4P39RP.js";var e;(function(r){r.Heavy="HEAVY",r.Medium="MEDIUM",r.Light="LIGHT"})(e||(e={}));var i;(function(r){r.Success="SUCCESS",r.Warning="WARNING",r.Error="ERROR"})(i||(i={}));const a=t("Haptics",{web:()=>n(()=>import("./web-Ci68E5o7.js"),__vite__mapDeps([0,1,2])).then(r=>new r.HapticsWeb)});export{a as Haptics,e as ImpactStyle,i as NotificationType};
