const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/DashboardStats-53b7Cr6r.js","assets/index-4uEDxtrq.js","assets/index-Cj7qK15l.css","assets/StatCard-C_JEo2Ua.js","assets/formatters-LGS2Cxr7.js","assets/credit-card-BHQw9W3V.js","assets/activity-BX4uixIz.js","assets/RecentActivities-BfCbV_kz.js","assets/card-lD3HLtIl.js","assets/QuickActions-B5ncPXOw.js","assets/DashboardExportButton-ChBQOdUs.js","assets/file-text-C3G0Jgl0.js","assets/file-spreadsheet-C9vZodGQ.js","assets/lucide-react-D2qNdiDw.js","assets/loader-circle-Dpy2PwT4.js","assets/tag-Dk40DvsN.js","assets/trash-Cjs83PIq.js","assets/arrow-left-BDyBAAt-.js","assets/eye-178cd5Up.js","assets/MobileHeader-Cww427qV.js","assets/map-pin-CR4xe2-0.js","assets/save-CM9FoLIv.js","assets/trash-2-CHZFZMDs.js","assets/user-x-Clydvlp0.js"])))=>i.map(i=>d[i]);
import{r as a,v as F,w as L,u as M,q as P,y as O,_ as x,s as e,x as y,z as w}from"./index-4uEDxtrq.js";import{P as R}from"./PageTitle-C5jjmlhE.js";import{h as j}from"./errorHandler-YMGAVORs.js";import{M as k}from"./MobileHeader-Cww427qV.js";import"./arrow-left-BDyBAAt-.js";const T=a.lazy(()=>x(()=>import("./DashboardStats-53b7Cr6r.js"),__vite__mapDeps([0,1,2,3,4,5,6]))),C=a.lazy(()=>x(()=>import("./RecentActivities-BfCbV_kz.js"),__vite__mapDeps([7,1,2,8,4]))),z=a.lazy(()=>x(()=>import("./QuickActions-B5ncPXOw.js"),__vite__mapDeps([9,1,2,8]))),D=a.lazy(()=>x(()=>import("./DashboardExportButton-ChBQOdUs.js").then(c=>c.D),__vite__mapDeps([10,1,2,4,11,12]))),Q=()=>{const[c,v]=a.useState(!0),[l,_]=a.useState({totalMembers:0,totalEvents:0,totalIncome:0,totalExpense:0,totalDues:0,netBalance:0}),[E,A]=a.useState([]),u=F(),{user:m,logout:N}=L(),{members:i,loading:h}=M(),{expenses:o,getTotalExpenses:f,loading:p}=P(),{isMobile:r}=O(),g=a.useCallback(async()=>{try{await N(),u("/admin/login")}catch(s){j(s,"AdminPage")}},[N,u]),d=a.useCallback(async()=>{try{const s=i.reduce((n,b)=>n+b.payment_amount,0),t=f();_({totalMembers:i.length,totalEvents:0,totalIncome:s,totalExpense:t,totalDues:s,netBalance:s-t});const{Users:B,TrendingDown:S}=await x(async()=>{const{Users:n,TrendingDown:b}=await import("./lucide-react-D2qNdiDw.js");return{Users:n,TrendingDown:b}},__vite__mapDeps([13,11,1,2,14,15,16,6,17,18,19,12,20,5,21,22,23])),I=[{type:"member",title:`${i.length} anggota terdaftar`,date:new Date().toISOString(),icon:B},...o.slice(-3).map(n=>({type:"expense",title:`${n.description} - ${n.amount.toLocaleString("id-ID",{style:"currency",currency:"IDR"})}`,date:n.date,icon:S}))];A(I)}catch(s){j(s,"AdminPage")}},[i,o,f]);return a.useEffect(()=>{(async()=>{try{v(!0),m?await d():await g()}catch(t){j(t,"AdminPage"),await g()}finally{v(!1)}})()},[m,g,d]),a.useEffect(()=>{h||d()},[i,h,d]),a.useEffect(()=>{p||d()},[o,p,d]),c||h||p?e.jsx(y.div,{className:"min-h-screen w-full px-4 sm:px-6 py-8 sm:py-10 bg-[#F9F9F9] text-[#5D534B]",initial:"initial",animate:"animate",exit:"exit",variants:w,children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-center mb-6 sm:mb-8",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-4 sm:mb-0",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-300 rounded-full animate-pulse"}),e.jsx("div",{className:"w-32 h-4 bg-gray-300 rounded animate-pulse"})]}),e.jsx("div",{className:"w-20 h-8 bg-gray-300 rounded animate-pulse"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 sm:mb-8",children:Array.from({length:3}).map((s,t)=>e.jsx("div",{className:"bg-white p-4 sm:p-6 rounded-lg border border-gray-200 shadow-sm",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-gray-200 rounded-full animate-pulse"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse"}),e.jsx("div",{className:"h-6 bg-gray-200 rounded w-1/2 animate-pulse"})]})]})},t))}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-1",children:[e.jsx("div",{className:"w-32 h-6 bg-gray-300 rounded mb-4 animate-pulse"}),e.jsx("div",{className:"bg-white p-4 rounded-lg border border-[#5D534B]/10 shadow-sm min-h-[200px]",children:e.jsx("div",{className:"space-y-3",children:Array.from({length:3}).map((s,t)=>e.jsxs("div",{className:"flex items-start p-2",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-300 rounded-full mr-3 animate-pulse"}),e.jsxs("div",{className:"flex-1 space-y-2",children:[e.jsx("div",{className:"w-3/4 h-4 bg-gray-300 rounded animate-pulse"}),e.jsx("div",{className:"w-1/2 h-3 bg-gray-300 rounded animate-pulse"})]})]},t))})})]}),e.jsxs("div",{className:"lg:col-span-2",children:[e.jsx("div",{className:"w-24 h-6 bg-gray-300 rounded mb-4 animate-pulse"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Array.from({length:4}).map((s,t)=>e.jsx("div",{className:"bg-white p-4 sm:p-5 rounded-lg border border-[#5D534B]/10 shadow-sm",children:e.jsxs("div",{className:"flex items-start space-x-3 sm:space-x-4",children:[e.jsx("div",{className:"w-10 h-10 sm:w-12 sm:h-12 bg-gray-300 rounded-full animate-pulse"}),e.jsxs("div",{className:"flex-1 space-y-2",children:[e.jsx("div",{className:"w-3/4 h-5 bg-gray-300 rounded animate-pulse"}),e.jsx("div",{className:"w-full h-4 bg-gray-300 rounded animate-pulse"})]})]})},t))})]})]})]})}):m?e.jsxs(e.Fragment,{children:[r&&e.jsx(k,{title:"BENDAHARA",showNotifications:!0}),e.jsxs(y.div,{className:`min-h-screen w-full px-4 sm:px-6 py-8 sm:py-10 bg-[#F9F9F9] text-[#5D534B] ${r?"pt-20 pb-24":""}`,initial:"initial",animate:"animate",exit:"exit",variants:w,children:[e.jsx(R,{title:"BENDAHARA"}),e.jsxs("div",{className:"max-w-7xl mx-auto",children:[!r&&e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-center mb-6 sm:mb-8",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-4 sm:mb-0",children:[e.jsx("div",{className:"p-2 bg-[#DDD6F3] rounded-full",children:e.jsx("div",{className:"w-5 h-5 bg-[#B39DDB] rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-white font-semibold text-xs",children:m?.email?.charAt(0).toUpperCase()})})}),e.jsx("span",{className:"font-medium text-[#5D534B] text-sm sm:text-base",children:m.email})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(a.Suspense,{fallback:e.jsx("div",{className:"w-32 h-10 bg-gray-200 rounded animate-pulse"}),children:e.jsx(D,{dashboardData:{totalMembers:l.totalMembers,totalIncome:l.totalIncome,totalExpense:l.totalExpense,totalDues:l.totalDues,netBalance:l.netBalance,members:i.map(s=>({...s,created_at:new Date().toISOString()})),expenses:o.map(s=>({...s,created_at:new Date().toISOString()}))}})}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs("button",{type:"button",onClick:()=>u("/"),className:"px-3 py-2 border border-[#FCE09B] text-[#5D534B] rounded-lg hover:bg-[#FCE09B] transition-colors text-sm flex items-center space-x-1",children:[e.jsx("span",{children:"🏠"}),e.jsx("span",{className:"hidden sm:inline",children:"Beranda"})]}),e.jsx("button",{type:"button",onClick:g,className:"px-4 py-2 border border-[#FF9898] text-[#5D534B] rounded-lg hover:bg-[#FF9898] transition-colors text-sm sm:text-base",children:"Logout"})]})]})]}),e.jsx(a.Suspense,{fallback:e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8",children:Array.from({length:6}).map((s,t)=>e.jsx("div",{className:"animate-pulse",children:e.jsx("div",{className:"bg-white p-4 sm:p-6 rounded-lg border border-gray-200 shadow-sm",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-gray-200 rounded-full"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),e.jsx("div",{className:"h-6 bg-gray-200 rounded w-1/2"})]})]})})},t))}),children:e.jsx(T,{stats:l,isLoading:c})}),r&&e.jsx("div",{className:"mb-6",children:e.jsx(a.Suspense,{fallback:e.jsx("div",{className:"w-full h-12 bg-gray-200 rounded animate-pulse"}),children:e.jsx(D,{dashboardData:{totalMembers:l.totalMembers,totalIncome:l.totalIncome,totalExpense:l.totalExpense,totalDues:l.totalDues,netBalance:l.netBalance,members:i.map(s=>({...s,created_at:new Date().toISOString()})),expenses:o.map(s=>({...s,created_at:new Date().toISOString()}))},className:"w-full"})})}),e.jsxs("div",{className:`grid gap-6 ${r?"grid-cols-1":"grid-cols-1 lg:grid-cols-3"}`,children:[e.jsx("div",{className:r?"col-span-1":"lg:col-span-1",children:e.jsx(a.Suspense,{fallback:e.jsxs("div",{className:"animate-pulse",children:[e.jsx("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),e.jsx("div",{className:"bg-white p-4 rounded-lg border border-gray-200 shadow-sm min-h-[200px]",children:e.jsx("div",{className:"space-y-3",children:Array.from({length:4}).map((s,t)=>e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-200 rounded-full"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-1"}),e.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},t))})})]}),children:e.jsx(C,{activities:E,isLoading:c})})}),!r&&e.jsxs("div",{className:"lg:col-span-2",children:[e.jsx("h3",{className:"text-base sm:text-lg font-bold mb-3 sm:mb-4 text-[#5D534B]",children:"Menu Utama"}),e.jsx(a.Suspense,{fallback:e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:Array.from({length:4}).map((s,t)=>e.jsx("div",{className:"animate-pulse",children:e.jsx("div",{className:"bg-white p-4 sm:p-6 rounded-lg border border-gray-200 shadow-sm",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"w-12 h-12 bg-gray-200 rounded-xl"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"h-5 bg-gray-200 rounded w-3/4 mb-2"}),e.jsx("div",{className:"h-4 bg-gray-200 rounded w-full"})]})]})})},t))}),children:e.jsx(z,{})})]})]})]})]})]}):null};export{Q as default};
