import React from 'react';

interface LogoProps {
  size?: number;
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ size = 40, className = '' }) => {
  return (
    <div
      className={`flex items-center justify-center rounded-full bg-primary text-primary-foreground font-bold ${className}`}
      style={{ width: size, height: size }}
    >
      <span style={{ fontSize: size * 0.4 }}>PPP</span>
    </div>
  );
};

export default Logo; 