@echo off
echo.
echo ==========================================
echo   FIXING DUPLICATE RESOURCES ERROR
echo ==========================================
echo.

echo Step 1: Cleaning Android build...
cd android
call gradlew clean
cd ..

echo.
echo Step 2: Cleaning web build...
rmdir /s /q dist

echo.
echo Step 3: Rebuilding web app...
call npm run build:mobile

echo.
echo Step 4: Syncing Capacitor...
call npx cap sync android

echo.
echo Step 5: Copying assets...
call npx cap copy android

echo.
echo ==========================================
echo   CLEANUP COMPLETED!
echo ==========================================
echo.
echo Now try running the app again in Android Studio.
echo.
pause
