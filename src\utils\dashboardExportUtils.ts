import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { formatRupiah, formatDate } from './formatters';

// Extend jsPDF type to include autoTable
declare module 'jspdf' {
  interface jsPDF {
    lastAutoTable: {
      finalY: number;
    };
  }
}

// Types for dashboard export
interface DashboardData {
  totalMembers: number;
  totalIncome: number;
  totalExpense: number;
  totalDues: number;
  netBalance: number;
  members: Array<{
    id: string;
    name: string;
    email?: string;
    phone?: string;
    payment_status: 'paid' | 'unpaid';
    payment_amount?: number;
    payment_date?: string;
    created_at: string;
  }>;
  expenses: Array<{
    id: string;
    description: string;
    amount: number;
    category: string;
    date: string;
    created_at: string;
  }>;
}

// PDF Export for Complete Dashboard Report
export const exportDashboardToPDF = (data: DashboardData, title: string = '<PERSON><PERSON><PERSON> DANAPEMUDA') => {
  const doc = new jsPDF();
  let currentY = 20;

  // Header
  doc.setFontSize(20);
  doc.setFont('helvetica', 'bold');
  doc.text('DANAPEMUDA', 105, currentY, { align: 'center' });
  currentY += 10;
  
  doc.setFontSize(16);
  doc.text(title, 105, currentY, { align: 'center' });
  currentY += 10;
  
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  doc.text(`Tanggal: ${formatDate(new Date().toISOString())}`, 105, currentY, { align: 'center' });
  currentY += 20;

  // Financial Summary Section
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('RINGKASAN KEUANGAN', 20, currentY);
  currentY += 10;

  const summaryData = [
    ['Total Anggota', data.totalMembers.toString()],
    ['Total Pemasukan', formatRupiah(data.totalIncome)],
    ['Total Pengeluaran', formatRupiah(data.totalExpense)],
    ['Total Iuran Terkumpul', formatRupiah(data.totalDues)],
    ['Saldo Bersih', formatRupiah(data.netBalance)]
  ];

  autoTable(doc, {
    body: summaryData,
    startY: currentY,
    styles: {
      fontSize: 10,
      cellPadding: 4,
    },
    columnStyles: {
      0: { fontStyle: 'bold', fillColor: [252, 224, 155] }, // #FCE09B
      1: { fontStyle: 'bold', textColor: data.netBalance >= 0 ? [0, 128, 0] : [255, 0, 0] }
    },
    margin: { left: 20, right: 20 }
  });

  currentY = doc.lastAutoTable.finalY + 20;

  // Members Status Section
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('STATUS PEMBAYARAN ANGGOTA', 20, currentY);
  currentY += 10;

  const paidMembers = data.members.filter(m => m.payment_status === 'paid');
  const unpaidMembers = data.members.filter(m => m.payment_status === 'unpaid');

  const memberStatusData = [
    ['Anggota Lunas', paidMembers.length.toString()],
    ['Anggota Belum Lunas', unpaidMembers.length.toString()],
    ['Total Anggota', data.totalMembers.toString()]
  ];

  autoTable(doc, {
    body: memberStatusData,
    startY: currentY,
    styles: {
      fontSize: 10,
      cellPadding: 4,
    },
    columnStyles: {
      0: { fontStyle: 'bold', fillColor: [179, 157, 219] }, // #B39DDB
      1: { fontStyle: 'bold' }
    },
    margin: { left: 20, right: 20 }
  });

  currentY = doc.lastAutoTable.finalY + 20;

  // Check if we need a new page
  if (currentY > 250) {
    doc.addPage();
    currentY = 20;
  }

  // Detailed Members List
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('DAFTAR ANGGOTA DETAIL', 20, currentY);
  currentY += 10;

  const membersTableData = data.members.map((member, index) => [
    index + 1,
    member.name,
    member.email || '-',
    member.payment_status === 'paid' ? 'LUNAS' : 'BELUM LUNAS',
    member.payment_amount ? formatRupiah(member.payment_amount) : '-',
    member.payment_date ? formatDate(member.payment_date) : '-'
  ]);

  autoTable(doc, {
    head: [['No', 'Nama', 'Email', 'Status', 'Jumlah', 'Tgl Bayar']],
    body: membersTableData,
    startY: currentY,
    styles: {
      fontSize: 8,
      cellPadding: 2,
    },
    headStyles: {
      fillColor: [93, 83, 75], // #5D534B
      textColor: 255,
      fontStyle: 'bold'
    },
    didParseCell: (data) => {
      // Color code payment status
      if (data.column.index === 3) { // Status column
        if (data.cell.text[0] === 'LUNAS') {
          data.cell.styles.textColor = [0, 128, 0]; // Green
          data.cell.styles.fontStyle = 'bold';
        } else {
          data.cell.styles.textColor = [255, 0, 0]; // Red
          data.cell.styles.fontStyle = 'bold';
        }
      }
    },
    margin: { left: 10, right: 10 }
  });

  currentY = doc.lastAutoTable.finalY + 20;

  // Add new page for expenses if needed
  if (currentY > 200) {
    doc.addPage();
    currentY = 20;
  }

  // Expenses Section
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('RINCIAN PENGELUARAN', 20, currentY);
  currentY += 10;

  if (data.expenses.length > 0) {
    const expensesTableData = data.expenses.map((expense, index) => [
      index + 1,
      expense.description,
      expense.category,
      formatRupiah(expense.amount),
      formatDate(expense.date)
    ]);

    // Add total row
    expensesTableData.push(['', '', 'TOTAL', formatRupiah(data.totalExpense), '']);

    autoTable(doc, {
      head: [['No', 'Deskripsi', 'Kategori', 'Jumlah', 'Tanggal']],
      body: expensesTableData,
      startY: currentY,
      styles: {
        fontSize: 8,
        cellPadding: 2,
      },
      headStyles: {
        fillColor: [255, 152, 152], // #FF9898
        textColor: [93, 83, 75], // #5D534B
        fontStyle: 'bold'
      },
      didParseCell: (data) => {
        // Style total row
        if (data.row.index === expensesTableData.length - 1) {
          data.cell.styles.fillColor = [252, 224, 155]; // #FCE09B
          data.cell.styles.fontStyle = 'bold';
        }
      },
      margin: { left: 10, right: 10 }
    });
  } else {
    doc.setFontSize(10);
    doc.text('Belum ada data pengeluaran', 20, currentY);
  }

  // Footer on all pages
  const pageCount = doc.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.text(`Halaman ${i} dari ${pageCount} - Generated by DANAPEMUDA`, 105, 290, { align: 'center' });
  }

  // Save
  doc.save(`laporan-lengkap-danapemuda-${new Date().toISOString().split('T')[0]}.pdf`);
};

// Excel Export for Complete Dashboard Report
export const exportDashboardToExcel = (data: DashboardData, filename: string = 'laporan-lengkap-danapemuda') => {
  const workbook = XLSX.utils.book_new();

  // Summary Sheet
  const summaryData = [
    ['LAPORAN LENGKAP DANAPEMUDA'],
    [`Tanggal: ${formatDate(new Date().toISOString())}`],
    [],
    ['RINGKASAN KEUANGAN'],
    ['Total Anggota', data.totalMembers],
    ['Total Pemasukan', data.totalIncome],
    ['Total Pengeluaran', data.totalExpense],
    ['Total Iuran Terkumpul', data.totalDues],
    ['Saldo Bersih', data.netBalance],
    [],
    ['STATUS ANGGOTA'],
    ['Anggota Lunas', data.members.filter(m => m.payment_status === 'paid').length],
    ['Anggota Belum Lunas', data.members.filter(m => m.payment_status === 'unpaid').length],
  ];

  const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
  summarySheet['!cols'] = [{ width: 25 }, { width: 20 }];
  XLSX.utils.book_append_sheet(workbook, summarySheet, 'Ringkasan');

  // Members Sheet
  const membersData = [
    ['DAFTAR ANGGOTA LENGKAP'],
    [`Tanggal: ${formatDate(new Date().toISOString())}`],
    [],
    ['No', 'Nama', 'Email', 'Telepon', 'Status Pembayaran', 'Jumlah Pembayaran', 'Tanggal Pembayaran', 'Tanggal Daftar'],
    ...data.members.map((member, index) => [
      index + 1,
      member.name,
      member.email || '-',
      member.phone || '-',
      member.payment_status === 'paid' ? 'LUNAS' : 'BELUM LUNAS',
      member.payment_amount || 0,
      member.payment_date ? formatDate(member.payment_date) : '-',
      formatDate(member.created_at)
    ])
  ];

  const membersSheet = XLSX.utils.aoa_to_sheet(membersData);
  membersSheet['!cols'] = [
    { width: 5 }, { width: 20 }, { width: 25 }, { width: 15 },
    { width: 15 }, { width: 15 }, { width: 15 }, { width: 15 }
  ];
  XLSX.utils.book_append_sheet(workbook, membersSheet, 'Data Anggota');

  // Expenses Sheet
  const expensesData = [
    ['RINCIAN PENGELUARAN'],
    [`Tanggal: ${formatDate(new Date().toISOString())}`],
    [],
    ['No', 'Deskripsi', 'Kategori', 'Jumlah', 'Tanggal'],
    ...data.expenses.map((expense, index) => [
      index + 1,
      expense.description,
      expense.category,
      expense.amount,
      formatDate(expense.date)
    ]),
    [],
    ['', '', 'TOTAL PENGELUARAN', data.totalExpense, '']
  ];

  const expensesSheet = XLSX.utils.aoa_to_sheet(expensesData);
  expensesSheet['!cols'] = [
    { width: 5 }, { width: 30 }, { width: 15 }, { width: 15 }, { width: 15 }
  ];
  XLSX.utils.book_append_sheet(workbook, expensesSheet, 'Data Pengeluaran');

  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  const data_blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  
  saveAs(data_blob, `${filename}-${new Date().toISOString().split('T')[0]}.xlsx`);
};
