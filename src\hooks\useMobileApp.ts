import { useState, useEffect } from 'react';
import { Capacitor } from '@capacitor/core';
import { Device } from '@capacitor/device';

interface DeviceInfo {
  model: string;
  platform: 'ios' | 'android' | 'web';
  operatingSystem: string;
  osVersion: string;
  manufacturer: string;
  isVirtual: boolean;
  webViewVersion: string;
}

export const useMobileApp = () => {
  const [isMobileApp, setIsMobileApp] = useState(false);
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [isAndroid, setIsAndroid] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkPlatform = async () => {
      try {
        const isNative = Capacitor.isNativePlatform();
        setIsMobileApp(isNative);

        if (isNative) {
          const info = await Device.getInfo();
          setDeviceInfo(info);
          setIsAndroid(info.platform === 'android');
          setIsIOS(info.platform === 'ios');
        } else {
          // Web platform
          setIsAndroid(false);
          setIsIOS(false);
        }
      } catch (error) {
        console.error('Error checking platform:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkPlatform();
  }, []);

  return {
    isMobileApp,
    isAndroid,
    isIOS,
    deviceInfo,
    isNative: Capacitor.isNativePlatform(),
    platform: Capacitor.getPlatform(),
    isLoading,
    // Helper methods
    isWeb: !Capacitor.isNativePlatform(),
    canUseNativeFeatures: Capacitor.isNativePlatform(),
  };
};
