import{t as r,v as d,O as t}from"./index-F6RtwXXX.js";const o=r.forwardRef(({className:a,...e},s)=>d.jsx("div",{ref:s,className:t("rounded-lg border bg-card text-card-foreground shadow-sm",a),...e}));o.displayName="Card";const c=r.forwardRef(({className:a,...e},s)=>d.jsx("div",{ref:s,className:t("flex flex-col space-y-1.5 p-6",a),...e}));c.displayName="CardHeader";const i=r.forwardRef(({className:a,...e},s)=>d.jsx("h3",{ref:s,className:t("text-2xl font-semibold leading-none tracking-tight",a),...e}));i.displayName="CardTitle";const n=r.forwardRef(({className:a,...e},s)=>d.jsx("p",{ref:s,className:t("text-sm text-muted-foreground",a),...e}));n.displayName="CardDescription";const l=r.forwardRef(({className:a,...e},s)=>d.jsx("div",{ref:s,className:t("p-6 pt-0",a),...e}));l.displayName="CardContent";const m=r.forwardRef(({className:a,...e},s)=>d.jsx("div",{ref:s,className:t("flex items-center p-6 pt-0",a),...e}));m.displayName="CardFooter";export{o as C,l as a,c as b,i as c};
