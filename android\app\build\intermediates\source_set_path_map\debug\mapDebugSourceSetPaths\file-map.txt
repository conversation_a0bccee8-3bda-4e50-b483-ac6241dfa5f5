com.danapemuda.app-core-1.15.0-0 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\0532590b7a4aab564854b7a52547b35e\transformed\core-1.15.0\res
com.danapemuda.app-webkit-1.12.1-1 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\1e9f9d4e52736765d0defbaa620c661a\transformed\webkit-1.12.1\res
com.danapemuda.app-lifecycle-viewmodel-2.6.2-2 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\3b1223c7318c32d8ddf448b686e3d62c\transformed\lifecycle-viewmodel-2.6.2\res
com.danapemuda.app-core-runtime-2.2.0-3 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\4333f0d78f42412dd1a736bbf801f75f\transformed\core-runtime-2.2.0\res
com.danapemuda.app-lifecycle-viewmodel-savedstate-2.6.2-4 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\5a72866daff36140bd357dd10fe2764b\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.danapemuda.app-lifecycle-livedata-2.6.2-5 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\614552cc2dfb25e5371e8f31dd2bbfe8\transformed\lifecycle-livedata-2.6.2\res
com.danapemuda.app-coordinatorlayout-1.2.0-6 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\650e63120191e2feb54578405e710aea\transformed\coordinatorlayout-1.2.0\res
com.danapemuda.app-annotation-experimental-1.4.1-7 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\715844792b86ba67cf6594141f87334c\transformed\annotation-experimental-1.4.1\res
com.danapemuda.app-startup-runtime-1.1.1-8 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\825c30e9ea42bf4ed6d45b5b9ab088b8\transformed\startup-runtime-1.1.1\res
com.danapemuda.app-appcompat-resources-1.7.0-9 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\882e9826831c84fa56e9597b293c0dbe\transformed\appcompat-resources-1.7.0\res
com.danapemuda.app-core-splashscreen-1.0.1-10 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9a12f9b7c714b90d55120ff2ca57fa76\transformed\core-splashscreen-1.0.1\res
com.danapemuda.app-profileinstaller-1.3.1-11 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\9ad8261d5ae554f8f8e34c16f3052dc9\transformed\profileinstaller-1.3.1\res
com.danapemuda.app-lifecycle-runtime-2.6.2-12 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\a330c5b4508fadc3e1c479ef6ea847a3\transformed\lifecycle-runtime-2.6.2\res
com.danapemuda.app-tracing-1.2.0-13 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\b634fc6e99470cbb4a638a5db4faca77\transformed\tracing-1.2.0\res
com.danapemuda.app-lifecycle-process-2.6.2-14 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\d3aaa5e1ecdbd6908da0b8ab0b99cd3c\transformed\lifecycle-process-2.6.2\res
com.danapemuda.app-emoji2-views-helper-1.3.0-15 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\d802673b5c65bd4f69c2af85f74dce13\transformed\emoji2-views-helper-1.3.0\res
com.danapemuda.app-fragment-1.8.4-16 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\e0b151faef205b1fb0ecc1f7dea5fb16\transformed\fragment-1.8.4\res
com.danapemuda.app-emoji2-1.3.0-17 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ee314b8ac8ef8e018671188902e26743\transformed\emoji2-1.3.0\res
com.danapemuda.app-activity-1.9.2-18 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\eff3e8468e5145533b67659a4d77907a\transformed\activity-1.9.2\res
com.danapemuda.app-savedstate-1.2.1-19 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\f1f125a2347d5754a7bde40f319486da\transformed\savedstate-1.2.1\res
com.danapemuda.app-lifecycle-livedata-core-2.6.2-20 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\f567f833e0cbc34fc15a25ae649ab2ce\transformed\lifecycle-livedata-core-2.6.2\res
com.danapemuda.app-core-ktx-1.15.0-21 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\f9b12375caec2bee24deedc8f4f95ba7\transformed\core-ktx-1.15.0\res
com.danapemuda.app-appcompat-1.7.0-22 C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\fbf670d58628bfe6bf436fadb7bbd27c\transformed\appcompat-1.7.0\res
com.danapemuda.app-pngs-23 D:\PMD\android\app\build\generated\res\pngs\debug
com.danapemuda.app-resValues-24 D:\PMD\android\app\build\generated\res\resValues\debug
com.danapemuda.app-packageDebugResources-25 D:\PMD\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.danapemuda.app-packageDebugResources-26 D:\PMD\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.danapemuda.app-debug-27 D:\PMD\android\app\build\intermediates\merged_res\debug\mergeDebugResources
com.danapemuda.app-debug-28 D:\PMD\android\app\src\debug\res
com.danapemuda.app-main-29 D:\PMD\android\app\src\main\res
com.danapemuda.app-debug-30 D:\PMD\android\capacitor-cordova-android-plugins\build\intermediates\packaged_res\debug\packageDebugResources
com.danapemuda.app-debug-31 D:\PMD\node_modules\@capacitor\android\capacitor\build\intermediates\packaged_res\debug\packageDebugResources
com.danapemuda.app-debug-32 D:\PMD\node_modules\@capacitor\app\android\build\intermediates\packaged_res\debug\packageDebugResources
com.danapemuda.app-debug-33 D:\PMD\node_modules\@capacitor\device\android\build\intermediates\packaged_res\debug\packageDebugResources
com.danapemuda.app-debug-34 D:\PMD\node_modules\@capacitor\haptics\android\build\intermediates\packaged_res\debug\packageDebugResources
com.danapemuda.app-debug-35 D:\PMD\node_modules\@capacitor\keyboard\android\build\intermediates\packaged_res\debug\packageDebugResources
com.danapemuda.app-debug-36 D:\PMD\node_modules\@capacitor\network\android\build\intermediates\packaged_res\debug\packageDebugResources
com.danapemuda.app-debug-37 D:\PMD\node_modules\@capacitor\splash-screen\android\build\intermediates\packaged_res\debug\packageDebugResources
com.danapemuda.app-debug-38 D:\PMD\node_modules\@capacitor\status-bar\android\build\intermediates\packaged_res\debug\packageDebugResources
