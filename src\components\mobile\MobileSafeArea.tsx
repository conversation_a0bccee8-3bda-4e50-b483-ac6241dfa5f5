import React from 'react';
import { useMobileApp } from '../../hooks/useMobileApp';

interface MobileSafeAreaProps {
  children: React.ReactNode;
  className?: string;
  top?: boolean;
  bottom?: boolean;
  left?: boolean;
  right?: boolean;
}

const MobileSafeArea: React.FC<MobileSafeAreaProps> = ({ 
  children, 
  className = '',
  top = true,
  bottom = true,
  left = true,
  right = true
}) => {
  const { isMobileApp, isAndroid, isIOS } = useMobileApp();

  // Build safe area classes based on platform and props
  const getSafeAreaClasses = () => {
    if (!isMobileApp) return className;

    const safeAreaClasses = [];
    
    if (top) safeAreaClasses.push('pt-safe-top');
    if (bottom) safeAreaClasses.push('pb-safe-bottom');
    if (left) safeAreaClasses.push('pl-safe-left');
    if (right) safeAreaClasses.push('pr-safe-right');

    // Platform-specific adjustments
    if (isAndroid) {
      // Android-specific safe area handling
      safeAreaClasses.push('android-safe-area');
    }
    
    if (isIOS) {
      // iOS-specific safe area handling
      safeAreaClasses.push('ios-safe-area');
    }

    return `${safeAreaClasses.join(' ')} ${className}`.trim();
  };

  return (
    <div className={getSafeAreaClasses()}>
      {children}
    </div>
  );
};

export default MobileSafeArea;
