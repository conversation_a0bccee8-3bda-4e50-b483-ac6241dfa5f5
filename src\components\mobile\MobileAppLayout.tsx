import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { MessageSquare, User } from 'lucide-react';
import { useMobileApp } from '../../hooks/useMobileApp';
import MobileStatusBar from './MobileStatusBar';
import MobileSafeArea from './MobileSafeArea';
import MobileUserProfile from './MobileUserProfile';
import { ChatProvider } from '../../context/ChatContext';
import ChatContainer from '../chat/ChatContainer';
import { App } from '@capacitor/app';
import { Keyboard } from '@capacitor/keyboard';

interface MobileAppLayoutProps {
  children: React.ReactNode;
  statusBarStyle?: 'DARK' | 'LIGHT';
  statusBarColor?: string;
  className?: string;
}

const MobileAppLayout: React.FC<MobileAppLayoutProps> = ({
  children,
  statusBarStyle = 'DARK',
  statusBarColor = '#5D534B',
  className = ''
}) => {
  const { isMobileApp } = useMobileApp();
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);

  useEffect(() => {
    if (isMobileApp) {
      let appStateListener: any;
      let keyboardShowListener: any;
      let keyboardHideListener: any;
      let backButtonListener: any;

      const setupListeners = async () => {
        // Handle app state changes
        const handleAppStateChange = (state: { isActive: boolean }) => {
          if (process.env.NODE_ENV === 'development') {
            console.warn('App state changed:', state);
          }
        };

        // Handle keyboard events
        const handleKeyboardShow = () => {
          document.body.classList.add('keyboard-open');
        };

        const handleKeyboardHide = () => {
          document.body.classList.remove('keyboard-open');
        };

        // Handle back button (Android)
        const handleBackButton = () => {
          // Custom back button logic
          if (process.env.NODE_ENV === 'development') {
            console.warn('Back button pressed');
          }
          return false; // Prevent default behavior
        };

        // Add event listeners
        appStateListener = await App.addListener('appStateChange', handleAppStateChange);
        keyboardShowListener = await Keyboard.addListener('keyboardWillShow', handleKeyboardShow);
        keyboardHideListener = await Keyboard.addListener('keyboardWillHide', handleKeyboardHide);
        backButtonListener = await App.addListener('backButton', handleBackButton);
      };

      setupListeners();

      // Cleanup
      return () => {
        if (appStateListener) appStateListener.remove();
        if (keyboardShowListener) keyboardShowListener.remove();
        if (keyboardHideListener) keyboardHideListener.remove();
        if (backButtonListener) backButtonListener.remove();
      };
    }
  }, [isMobileApp]);

  // If not a mobile app, render children directly
  if (!isMobileApp) {
    return <div className={className}>{children}</div>;
  }

  return (
    <ChatProvider>
      <MobileStatusBar
        style={statusBarStyle}
        backgroundColor={statusBarColor}
      />
      <MobileSafeArea className={`min-h-screen-safe ${className}`}>
        {children}
      </MobileSafeArea>

      {/* Mobile-only Floating Action Buttons */}
      <div className="fixed bottom-6 right-6 flex flex-col space-y-3 z-40">
        {/* Profile Button */}
        <motion.button
          onClick={() => setIsProfileOpen(true)}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="w-12 h-12 bg-[#9DE0D2] border-4 border-[#5D534B] shadow-[4px_4px_0px_#5D534B] hover:shadow-[6px_6px_0px_#5D534B] transition-all rounded-full flex items-center justify-center"
        >
          <User className="w-5 h-5 text-[#5D534B]" />
        </motion.button>

        {/* Chat Button */}
        <motion.button
          onClick={() => setIsChatOpen(true)}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="w-14 h-14 bg-[#FCE09B] border-4 border-[#5D534B] shadow-[4px_4px_0px_#5D534B] hover:shadow-[6px_6px_0px_#5D534B] transition-all rounded-full flex items-center justify-center"
        >
          <MessageSquare className="w-6 h-6 text-[#5D534B]" />
        </motion.button>
      </div>

      {/* Mobile User Profile */}
      <MobileUserProfile
        isOpen={isProfileOpen}
        onClose={() => setIsProfileOpen(false)}
      />

      {/* Mobile Chat */}
      <ChatContainer
        isOpen={isChatOpen}
        onClose={() => setIsChatOpen(false)}
      />
    </ChatProvider>
  );
};

export default MobileAppLayout;
