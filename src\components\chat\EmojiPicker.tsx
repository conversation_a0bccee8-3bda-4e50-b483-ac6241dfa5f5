import React from 'react';
import { motion } from 'framer-motion';

interface EmojiPickerProps {
  onEmojiSelect: (emoji: string) => void;
  onClose: () => void;
}

const EmojiPicker: React.FC<EmojiPickerProps> = ({ onEmojiSelect, onClose }) => {
  const emojiCategories = {
    'Smileys': [
      '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
      '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
      '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜'
    ],
    'Gestures': [
      '👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙',
      '👈', '👉', '👆', '🖕', '👇', '☝️', '👋', '🤚',
      '🖐️', '✋', '🖖', '👏', '🙌', '🤲', '🤝', '🙏'
    ],
    'Hearts': [
      '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍',
      '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖',
      '💘', '💝', '💟', '♥️', '💯', '💢', '💥', '💫'
    ],
    'Objects': [
      '🎉', '🎊', '🎈', '🎁', '🎀', '🎂', '🍰', '🧁',
      '🍕', '🍔', '🍟', '🌭', '🥪', '🌮', '🌯', '🥙',
      '🥗', '🍝', '🍜', '🍲', '🍛', '🍣', '🍱', '🥟'
    ]
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9, y: 10 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.9, y: 10 }}
      className="absolute bottom-full right-0 mb-2 bg-white border-2 border-[#5D534B] shadow-[4px_4px_0px_#5D534B] rounded-lg p-4 w-80 max-h-64 overflow-y-auto z-50"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-3 pb-2 border-b-2 border-[#5D534B]">
        <h4 className="font-bold text-[#5D534B]">Pick an emoji</h4>
        <button
          onClick={onClose}
          className="text-[#5D534B] hover:bg-[#FCE09B]/20 p-1 rounded transition-colors"
        >
          ✕
        </button>
      </div>

      {/* Emoji Categories */}
      <div className="space-y-4">
        {Object.entries(emojiCategories).map(([category, emojis]) => (
          <div key={category}>
            <h5 className="text-sm font-bold text-[#5D534B] mb-2">{category}</h5>
            <div className="grid grid-cols-8 gap-2">
              {emojis.map((emoji) => (
                <button
                  key={emoji}
                  onClick={() => onEmojiSelect(emoji)}
                  className="w-8 h-8 flex items-center justify-center hover:bg-[#FCE09B]/20 rounded transition-colors text-lg"
                >
                  {emoji}
                </button>
              ))}
            </div>
          </div>
        ))}
      </div>
    </motion.div>
  );
};

export default EmojiPicker;
