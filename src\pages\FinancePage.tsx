import { useEffect, useState, useCallback } from 'react';
import { CreditCard, TrendingDown, Calendar, Users } from 'lucide-react';
import StatCard from '../components/StatCard';
import { formatRupiah, formatDate } from '../utils/formatters';
import Loader from '../components/Loader';
import { useMembersContext } from '../context/MembersContext';
import { useExpensesContext } from '../context/ExpensesContext';
import { handleError } from '../utils/errorHandler';

interface FinanceSummary {
  totalIncome: number;
  totalExpense: number;
  balance: number;
}

const FinancePage = () => {
  const { members, loading: membersLoading } = useMembersContext();
  const { expenses, getTotalExpenses, loading: expensesLoading } = useExpensesContext();
  const [summary, setSummary] = useState<FinanceSummary>({
    totalIncome: 0,
    totalExpense: 0,
    balance: 0
  });
  const [totalDues, setTotalDues] = useState<number>(0);
  const [netBalance, setNetBalance] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(true);

  const loadData = useCallback(async () => {
    try {
      setIsLoading(true);

      // Hitung total iuran dari semua anggota
      const duesTotal = members.reduce((sum, member) => sum + member.payment_amount, 0);

      // Hitung total pengeluaran dari ExpensesContext
      const totalExpense = getTotalExpenses();

      const summaryData: FinanceSummary = {
        totalIncome: duesTotal,
        totalExpense: totalExpense,
        balance: duesTotal - totalExpense
      };

      const remainingBalance = duesTotal - totalExpense;

      setSummary(summaryData);
      setTotalDues(duesTotal);
      setNetBalance(remainingBalance);
    } catch (error) {
      handleError(error, 'FinancePage: load financial data');
    } finally {
      setIsLoading(false);
    }
  }, [members, getTotalExpenses]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // Update data ketika members atau expenses berubah
  useEffect(() => {
    if (!membersLoading && !expensesLoading) {
      loadData();
    }
  }, [membersLoading, expensesLoading, loadData]);

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[50vh]">
        <Loader size="large" variant="secondary" text="Memuat Data Keuangan..." />
      </div>
    );
  }

  return (
    <div className="bg-[#F9F9F9] text-[#5D534B] rounded-2xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl md:text-3xl font-bold border-b-4 border-[#FF9898] pb-2">Keuangan</h1>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <StatCard 
          title="Total Iuran Anggota" 
          value={formatRupiah(totalDues)} 
          icon={Users}
          gradientClass="neo-gradient-blue" 
          borderColor="border-neo-blue"
        />
        <StatCard 
          title="Total Pengeluaran" 
          value={formatRupiah(summary.totalExpense)} 
          icon={TrendingDown}
          gradientClass="neo-gradient-pink" 
          borderColor="border-neo-pink"
        />
        <StatCard 
          title="Sisa Saldo" 
          value={formatRupiah(netBalance)} 
          icon={CreditCard}
          gradientClass="neo-gradient-yellow" 
          borderColor="border-neo-yellow"
        />
      </div>
      
      <div className="neo-card p-4 overflow-hidden animate-fade-in bg-white border-4 border-[#9DE0D2]">
        <h2 className="text-xl font-bold mb-4 flex items-center text-[#5D534B]">
          <TrendingDown className="mr-2" size={20} />
          Transaksi Pengeluaran
        </h2>
        
        <div className="space-y-4">
          {expenses.length > 0 ? (
            expenses
              .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()) // Urutkan terbaru dulu
              .map((expense) => (
                <div
                  key={expense.id}
                  className="expense-card bg-white p-4 border-2 border-[#5D534B] rounded-lg shadow-sm"
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="text-lg font-bold text-[#5D534B]">{expense.description}</h3>
                      <div className="flex items-center text-sm mt-1 text-[#5D534B]">
                        <Calendar size={14} className="mr-1" />
                        <span>{formatDate(expense.date)}</span>
                      </div>
                      {expense.category && (
                        <div className="mt-1">
                          <span className="inline-block px-2 py-1 text-xs border border-[#5D534B] rounded-full bg-[#FCE09B] text-[#5D534B]">
                            {expense.category}
                          </span>
                        </div>
                      )}
                    </div>
                    <div>
                      <span className="text-md font-bold px-3 py-1 border-2 border-[#5D534B] rounded-full bg-[#FF9898] text-[#5D534B]">
                        {formatRupiah(expense.amount)}
                      </span>
                    </div>
                  </div>
                </div>
              ))
          ) : (
            <div className="text-center py-8 text-[#5D534B]">
              <TrendingDown size={48} className="mx-auto mb-4 text-[#FF9898]" />
              <p className="text-lg font-medium">Belum ada transaksi pengeluaran</p>
              <p className="text-sm opacity-70">Pengeluaran akan muncul di sini setelah ditambahkan</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FinancePage;
