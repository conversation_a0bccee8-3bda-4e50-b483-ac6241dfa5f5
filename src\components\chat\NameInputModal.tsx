import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { User, MessageSquare } from 'lucide-react';

interface NameInputModalProps {
  isOpen: boolean;
  onSubmit: (name: string) => void;
}

const NameInputModal: React.FC<NameInputModalProps> = ({ isOpen, onSubmit }) => {
  const [name, setName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim() || isSubmitting) return;

    setIsSubmitting(true);
    try {
      onSubmit(name.trim());
    } catch (error) {
      console.error('Error submitting name:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className="bg-[#F9F9F9] border-4 border-[#5D534B] shadow-[8px_8px_0px_#5D534B] rounded-lg p-6 w-full max-w-md"
      >
        {/* Header */}
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-[#FCE09B] border-4 border-[#5D534B] rounded-full flex items-center justify-center mx-auto mb-4">
            <MessageSquare className="w-8 h-8 text-[#5D534B]" />
          </div>
          <h2 className="text-2xl font-black text-[#5D534B] mb-2">
            Selamat Datang di DANAPEMUDA Chat!
          </h2>
          <p className="text-[#5D534B]">
            Masukkan nama Anda untuk mulai chat
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-bold text-[#5D534B] mb-2">
              Nama Anda
            </label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[#5D534B]" />
              <input
                id="name"
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Masukkan nama Anda..."
                className="w-full pl-12 pr-4 py-3 border-4 border-[#5D534B] rounded-lg bg-white text-[#5D534B] placeholder-[#5D534B]/60 font-medium focus:outline-none focus:shadow-[4px_4px_0px_#5D534B] transition-all"
                maxLength={30}
                required
                autoFocus
                disabled={isSubmitting}
              />
            </div>
            <p className="text-xs text-[#5D534B]/60 mt-1">
              Maksimal 30 karakter
            </p>
          </div>

          <motion.button
            type="submit"
            disabled={!name.trim() || isSubmitting}
            whileTap={{ scale: 0.98 }}
            className="w-full bg-[#FCE09B] text-[#5D534B] py-3 px-6 font-black border-4 border-[#5D534B] shadow-[4px_4px_0px_#5D534B] hover:shadow-[6px_6px_0px_#5D534B] disabled:opacity-50 disabled:cursor-not-allowed transition-all rounded-lg"
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="animate-spin w-5 h-5 border-2 border-[#5D534B] border-t-transparent rounded-full"></div>
                <span>Menyiapkan Chat...</span>
              </div>
            ) : (
              'Mulai Chat'
            )}
          </motion.button>
        </form>

        {/* Info */}
        <div className="mt-4 p-3 bg-[#9DE0D2] border-2 border-[#5D534B] rounded-lg">
          <p className="text-xs text-[#5D534B] text-center">
            💡 Nama Anda akan tersimpan dan dapat digunakan kembali
          </p>
        </div>
      </motion.div>
    </div>
  );
};

export default NameInputModal;
