import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, Shield } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import MobileNavigation from './mobile/MobileNavigation';
import { useMobileOptimized } from '../hooks/useMobileOptimized';

type LayoutProps = {
  children: React.ReactNode;
};

const Layout = ({ children }: LayoutProps) => {
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { isMobile } = useMobileOptimized();

  const navLinks = [
    { title: "<PERSON><PERSON><PERSON>", path: "/" },
    { title: "Anggota", path: "/members" },
    { title: "Acara", path: "/events" },
  ];

  const getActiveClass = (path: string) => {
    return location.pathname === path 
      ? "bg-[#FCE09B] text-[#5D534B]" 
      : "bg-[#9DE0D2] text-[#5D534B] hover:bg-[#FCE09B]/70";
  };

  // Animasi untuk header
  const headerVariants = {
    hidden: { y: -100, opacity: 0 },
    visible: { 
      y: 0, 
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15,
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    }
  };

  // Animasi untuk menu item
  const menuItemVariants = {
    hidden: { y: -20, opacity: 0 },
    visible: { 
      y: 0, 
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  // Mobile menu animation variants
  const mobileMenuVariants = {
    hidden: { 
      height: 0, 
      opacity: 0,
      transition: {
        duration: 0.4,
        ease: [0.4, 0, 0.2, 1],
        when: "afterChildren"
      }
    },
    visible: { 
      height: 'auto', 
      opacity: 1,
      transition: {
        duration: 0.4,
        ease: [0, 0, 0.2, 1],
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    }
  };

  // Mobile menu item variants
  const mobileItemVariants = {
    hidden: { 
      opacity: 0, 
      y: 10,
      transition: {
        duration: 0.2
      } 
    },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.3,
        type: "spring",
        stiffness: 70,
        damping: 15
      }
    }
  };

  return (
    <div className="min-h-screen bg-[#F9F9F9] flex flex-col">
      {/* Header */}
      <motion.header
        className={`bg-[#FF9898] border-b-4 border-[#5D534B] ${isMobile ? 'relative z-30' : ''}`}
        variants={headerVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <motion.div 
              className="flex items-center"
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ type: 'spring', stiffness: 200, delay: 0.2 }}
            >
              <h1 className="text-3xl font-black text-[#5D534B]">DANA<span className="text-white">PEMUDA</span></h1>
            </motion.div>
            
            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-4">
              <ul className="flex space-x-4">
                {navLinks.map((link, index) => (
                  <motion.li 
                    key={link.path}
                    variants={menuItemVariants}
                    custom={index}
                  >
                    <Link
                      to={link.path}
                      className={`${getActiveClass(link.path)} border-4 border-[#5D534B] px-4 py-2 shadow-pastel-sm transition-all duration-200 rounded-full block`}
                    >
                      {link.title}
                    </Link>
                  </motion.li>
                ))}
              </ul>
            </nav>
            
            {/* Mobile Menu Button */}
            <motion.button
              className="md:hidden border-4 border-[#5D534B] p-2 shadow-pastel-sm bg-white text-[#5D534B] rounded-full"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              whileTap={{ scale: 0.9 }}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </motion.button>
          </div>
          
          {/* Mobile Navigation */}
          <AnimatePresence>
            {isMobileMenuOpen && (
              <motion.div 
                className="md:hidden mt-4 overflow-hidden"
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ 
                  duration: 0.5, 
                  ease: [0.33, 1, 0.68, 1],
                }}
              >
                <ul className="flex flex-col space-y-2">
                  {navLinks.map((link, index) => (
                    <motion.li 
                      key={link.path}
                      initial={{ x: -20, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      exit={{ x: -20, opacity: 0 }}
                      transition={{ 
                        delay: index * 0.1,
                        duration: 0.4,
                        ease: "easeInOut"
                      }}
                    >
                      <Link
                        to={link.path}
                        className={`${getActiveClass(link.path)} border-4 border-[#5D534B] px-4 py-2 shadow-pastel-sm rounded-full block transition-all duration-200`}
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        {link.title}
                      </Link>
                    </motion.li>
                  ))}
                </ul>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.header>
      
      {/* Main Content */}
      <main className={`container mx-auto px-4 py-8 flex-1 ${isMobile ? 'pb-20' : ''}`}>
        {children}
      </main>

      {/* Mobile Navigation */}
      <MobileNavigation />

      {/* Footer - Hidden on mobile */}
      {!isMobile && (
        <motion.footer
          className="bg-[#5D534B] text-white py-4 mt-auto"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.5 }}
        >
          <div className="container mx-auto px-4 text-center">
            <p>©DANAPEMUDA. GUYUB RUKUN SALAWASE</p>
          </div>
        </motion.footer>
      )}
    </div>
  );
};

export default Layout;
