# 🔒 FIREBASE SECURITY RULES SETUP GUIDE

## 📋 **STEP-BY-STEP TUTORIAL**

### **STEP 1: Buka Firebase Console**
1. **Buka browser** dan pergi ke: https://console.firebase.google.com
2. **Login** dengan akun Google yang sama dengan project
3. **Pilih project** "pemuda-psy" (project DANAPEMUDA)

### **STEP 2: Setup Firestore Rules**

#### **2.1 Navigasi ke Firestore:**
1. **Klik "Firestore Database"** di sidebar kiri
2. **Klik tab "Rules"** di bagian atas
3. **Lihat rules yang ada** (biasanya masih default)

#### **2.2 Replace dengan Chat Rules:**
1. **Hapus semua rules yang ada**
2. **Copy-paste rules berikut:**

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Users collection - users can read all, but only update their own
    match /users/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Chat rooms collection
    match /chatRooms/{roomId} {
      // Allow read if user is participant or it's a general room
      allow read: if request.auth != null && 
        (resource.data.type == 'general' || 
         request.auth.uid in resource.data.participants);
      
      // Allow create for authenticated users
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.createdBy;
      
      // Allow update if user is participant (for lastMessage updates)
      allow update: if request.auth != null && 
        (resource.data.type == 'general' || 
         request.auth.uid in resource.data.participants);
    }
    
    // Messages collection
    match /messages/{messageId} {
      // Allow read if user has access to the room
      allow read: if request.auth != null && 
        exists(/databases/$(database)/documents/chatRooms/$(resource.data.roomId)) &&
        (get(/databases/$(database)/documents/chatRooms/$(resource.data.roomId)).data.type == 'general' ||
         request.auth.uid in get(/databases/$(database)/documents/chatRooms/$(resource.data.roomId)).data.participants);
      
      // Allow create if user has access to the room and is the sender
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.senderId &&
        exists(/databases/$(database)/documents/chatRooms/$(request.resource.data.roomId)) &&
        (get(/databases/$(database)/documents/chatRooms/$(request.resource.data.roomId)).data.type == 'general' ||
         request.auth.uid in get(/databases/$(database)/documents/chatRooms/$(request.resource.data.roomId)).data.participants);
      
      // Allow update for reactions and edits by sender
      allow update: if request.auth != null && 
        (request.auth.uid == resource.data.senderId || 
         'reactions' in request.resource.data.diff(resource.data).affectedKeys());
    }
    
    // Typing indicators
    match /typing/{roomId}/users/{userId} {
      allow read, write: if request.auth != null && 
        exists(/databases/$(database)/documents/chatRooms/$(roomId)) &&
        (get(/databases/$(database)/documents/chatRooms/$(roomId)).data.type == 'general' ||
         request.auth.uid in get(/databases/$(database)/documents/chatRooms/$(roomId)).data.participants);
    }
    
    // Notifications
    match /notifications/{userId}/{notificationId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Existing collections (members, expenses, etc.)
    match /members/{document=**} {
      allow read, write: if request.auth != null;
    }
    
    match /expenses/{document=**} {
      allow read, write: if request.auth != null;
    }
    
    match /events/{document=**} {
      allow read, write: if request.auth != null;
    }
    
    match /dues_config/{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

#### **2.3 Publish Rules:**
1. **Klik "Publish"** button
2. **Tunggu konfirmasi** "Rules published successfully"

### **STEP 3: Setup Storage Rules**

#### **3.1 Navigasi ke Storage:**
1. **Klik "Storage"** di sidebar kiri
2. **Klik tab "Rules"** di bagian atas

#### **3.2 Replace dengan Storage Rules:**
1. **Hapus rules yang ada**
2. **Copy-paste rules berikut:**

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Chat file uploads
    match /chat/{roomId}/{fileName} {
      // Allow read if user is authenticated
      allow read: if request.auth != null;
      
      // Allow write if user is authenticated and file meets requirements
      allow write: if request.auth != null && 
        request.resource.size < 10 * 1024 * 1024 && // 10MB limit
        request.resource.contentType.matches('image/.*|application/pdf|text/.*|application/msword|application/vnd.openxmlformats-officedocument.wordprocessingml.document|application/zip|application/x-rar-compressed');
    }
    
    // Other file uploads (existing)
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

#### **3.3 Publish Storage Rules:**
1. **Klik "Publish"** button
2. **Tunggu konfirmasi** "Rules published successfully"

---

## ✅ **VERIFICATION STEPS**

### **1. Test Firestore Rules:**
1. **Buka Firebase Console** > **Firestore** > **Rules**
2. **Klik "Rules Playground"** (jika tersedia)
3. **Test dengan:**
   - **Authenticated user** reading `/chatRooms/general`
   - **Unauthenticated user** reading `/messages/test` (should fail)

### **2. Test Storage Rules:**
1. **Buka Firebase Console** > **Storage** > **Rules**
2. **Verify rules** are active
3. **Test file upload** dari aplikasi

### **3. Test dari Aplikasi:**
1. **Login ke aplikasi**
2. **Buka chat**
3. **Kirim pesan** (should work)
4. **Upload file** (should work)
5. **Logout dan coba akses** (should fail)

---

## 🚨 **TROUBLESHOOTING**

### **Common Issues:**

#### **1. Rules Syntax Error:**
```
Error: Expected 'allow' or 'match' but got...
```
**Solution:** Check syntax, missing semicolons, or brackets

#### **2. Permission Denied:**
```
FirebaseError: Missing or insufficient permissions
```
**Solution:** 
- Check if user is authenticated
- Verify rules match your data structure
- Check collection/document paths

#### **3. File Upload Fails:**
```
Storage: User does not have permission
```
**Solution:**
- Check Storage rules are published
- Verify file size < 10MB
- Check file type is allowed

### **Debug Steps:**
1. **Check Browser Console** for detailed error messages
2. **Firebase Console** > **Usage** > **Requests** for failed requests
3. **Test with Firebase Emulator** for local testing

---

## 🔧 **ADVANCED CONFIGURATION**

### **Custom Rules for DANAPEMUDA:**

#### **Admin-only Collections:**
```javascript
// Only admins can modify certain collections
match /admin_settings/{document=**} {
  allow read: if request.auth != null;
  allow write: if request.auth != null && 
    get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
}
```

#### **Rate Limiting:**
```javascript
// Limit message frequency
match /messages/{messageId} {
  allow create: if request.auth != null && 
    request.auth.uid == request.resource.data.senderId &&
    // Add rate limiting logic here
    true;
}
```

---

## 📊 **MONITORING**

### **Check Rules Performance:**
1. **Firebase Console** > **Firestore** > **Usage**
2. **Monitor:**
   - Read/Write operations
   - Failed requests
   - Security rule evaluations

### **Set up Alerts:**
1. **Firebase Console** > **Alerts**
2. **Create alerts for:**
   - High error rates
   - Unusual access patterns
   - Security rule violations

---

## ✅ **CHECKLIST**

### **Before Going Live:**
- [ ] Firestore rules published
- [ ] Storage rules published  
- [ ] Rules tested with authenticated users
- [ ] Rules tested with unauthenticated users
- [ ] File upload tested
- [ ] Chat functionality tested
- [ ] No console errors
- [ ] Performance monitoring setup

### **Security Best Practices:**
- [ ] Never allow unrestricted access
- [ ] Always validate user authentication
- [ ] Limit file upload sizes
- [ ] Restrict file types
- [ ] Monitor for abuse
- [ ] Regular security reviews

---

## 🎯 **QUICK SETUP COMMANDS**

### **If you prefer CLI:**
```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize project
firebase init firestore
firebase init storage

# Deploy rules
firebase deploy --only firestore:rules
firebase deploy --only storage
```

**FIREBASE SECURITY RULES SETUP COMPLETE! 🔒✅**
