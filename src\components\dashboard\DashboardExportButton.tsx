import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Download, FileText, FileSpreadsheet, ChevronDown, BarChart3 } from 'lucide-react';
import { Button } from '../ui/button';
import { useMobileOptimized } from '../../hooks/useMobileOptimized';
import { toast } from 'sonner';
import { exportDashboardToPDF, exportDashboardToExcel } from '../../utils/dashboardExportUtils';

interface DashboardExportButtonProps {
  dashboardData: {
    totalMembers: number;
    totalIncome: number;
    totalExpense: number;
    totalDues: number;
    netBalance: number;
    members: Array<{
      id: string;
      name: string;
      email?: string;
      phone?: string;
      payment_status: 'paid' | 'unpaid';
      payment_amount?: number;
      payment_date?: string;
      created_at: string;
    }>;
    expenses: Array<{
      id: string;
      description: string;
      amount: number;
      category: string;
      date: string;
      created_at: string;
    }>;
  };
  className?: string;
}

const DashboardExportButton: React.FC<DashboardExportButtonProps> = ({
  dashboardData,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const { isMobile } = useMobileOptimized();

  const handleExportPDF = async () => {
    try {
      setIsExporting(true);
      setIsOpen(false);
      
      await new Promise(resolve => setTimeout(resolve, 500)); // Small delay for UX
      
      exportDashboardToPDF(dashboardData, 'Laporan Lengkap DANAPEMUDA');
      toast.success('📄 Laporan PDF lengkap berhasil diunduh!');
    } catch (error) {
      console.error('Error exporting dashboard PDF:', error);
      toast.error('❌ Gagal mengunduh laporan PDF');
    } finally {
      setIsExporting(false);
    }
  };

  const handleExportExcel = async () => {
    try {
      setIsExporting(true);
      setIsOpen(false);
      
      await new Promise(resolve => setTimeout(resolve, 500)); // Small delay for UX
      
      exportDashboardToExcel(dashboardData, 'laporan-lengkap-danapemuda');
      toast.success('📊 Laporan Excel lengkap berhasil diunduh!');
    } catch (error) {
      console.error('Error exporting dashboard Excel:', error);
      toast.error('❌ Gagal mengunduh laporan Excel');
    } finally {
      setIsExporting(false);
    }
  };

  const isDisabled = dashboardData.totalMembers === 0 && dashboardData.expenses.length === 0;

  return (
    <div className={`relative ${className}`}>
      {/* Main Export Button */}
      <Button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        disabled={isDisabled || isExporting}
        className={`
          neo-button-pink flex items-center space-x-2 px-4 py-2 text-sm font-bold
          ${isMobile ? 'w-full justify-center' : ''}
          ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <BarChart3 size={16} />
        <span>{isMobile ? 'Export Laporan Lengkap' : 'Export Dashboard'}</span>
        <ChevronDown 
          size={14} 
          className={`transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
        />
      </Button>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <div 
              className="fixed inset-0 z-10"
              onClick={() => setIsOpen(false)}
            />
            
            {/* Menu */}
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.15 }}
              className={`
                absolute z-20 mt-2 bg-white border-2 border-[#5D534B] rounded-lg shadow-[4px_4px_0px_#5D534B] overflow-hidden
                ${isMobile ? 'left-0 right-0' : 'right-0 min-w-[280px]'}
              `}
            >
              {/* Header */}
              <div className="px-4 py-3 bg-[#FCE09B] border-b-2 border-[#5D534B]">
                <div className="font-bold text-[#5D534B] text-sm">Export Laporan Lengkap</div>
                <div className="text-xs text-[#5D534B] opacity-70">
                  Semua data anggota, keuangan & pengeluaran
                </div>
              </div>

              {/* PDF Export Option */}
              <button
                type="button"
                onClick={handleExportPDF}
                disabled={isExporting}
                className="w-full px-4 py-3 text-left hover:bg-[#FF9898] transition-colors duration-150 flex items-center space-x-3 text-[#5D534B] font-medium disabled:opacity-50"
              >
                <FileText size={20} className="text-red-600" />
                <div className="flex-1">
                  <div className="font-semibold">Export PDF Lengkap</div>
                  <div className="text-xs opacity-70">
                    Ringkasan + Anggota + Pengeluaran
                  </div>
                </div>
                <div className="text-xs bg-red-100 px-2 py-1 rounded text-red-700">
                  PDF
                </div>
              </button>

              {/* Divider */}
              <div className="border-t border-[#5D534B]/20" />

              {/* Excel Export Option */}
              <button
                type="button"
                onClick={handleExportExcel}
                disabled={isExporting}
                className="w-full px-4 py-3 text-left hover:bg-[#B39DDB] transition-colors duration-150 flex items-center space-x-3 text-[#5D534B] font-medium disabled:opacity-50"
              >
                <FileSpreadsheet size={20} className="text-green-600" />
                <div className="flex-1">
                  <div className="font-semibold">Export Excel Lengkap</div>
                  <div className="text-xs opacity-70">
                    3 Sheet: Ringkasan + Anggota + Pengeluaran
                  </div>
                </div>
                <div className="text-xs bg-green-100 px-2 py-1 rounded text-green-700">
                  XLSX
                </div>
              </button>

              {/* Info Footer */}
              <div className="px-4 py-2 bg-[#F9F9F9] border-t border-[#5D534B]/20">
                <div className="text-xs text-[#5D534B] opacity-60 text-center">
                  📊 {dashboardData.totalMembers} anggota • 💰 {dashboardData.expenses.length} pengeluaran
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Loading Overlay */}
      {isExporting && (
        <div className="absolute inset-0 bg-white/90 flex items-center justify-center rounded-lg z-30">
          <div className="flex items-center space-x-2 text-[#5D534B]">
            <div className="w-4 h-4 border-2 border-[#5D534B] border-t-transparent rounded-full animate-spin"></div>
            <span className="text-sm font-medium">Generating...</span>
          </div>
        </div>
      )}

      {/* Disabled State Info */}
      {isDisabled && (
        <div className="absolute -bottom-8 left-0 right-0 text-center">
          <div className="text-xs text-[#5D534B] opacity-60">
            Tidak ada data untuk diexport
          </div>
        </div>
      )}
    </div>
  );
};

export default DashboardExportButton;
