import{r,s as e,K as j,V as _,t as f,B as v,J as l}from"./index-4uEDxtrq.js";import{P as y}from"./PageTitle-C5jjmlhE.js";import{I as d}from"./input-DyKMA-rb.js";import{S as N}from"./save-CM9FoLIv.js";const b=r.forwardRef(({className:a,...o},i)=>e.jsx("textarea",{className:j("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:i,...o}));b.displayName="Textarea";const I=()=>{const{config:a,loading:o,updateConfig:i}=_(),[n,c]=r.useState({pemuda_amount:0,pemudi_amount:0,pelajar_amount:0,notes:""}),[p,x]=r.useState(!1);r.useEffect(()=>{a&&c({pemuda_amount:a.pemuda_amount,pemudi_amount:a.pemudi_amount,pelajar_amount:a.pelajar_amount,notes:a.notes})},[a]);const t=u=>{const{name:s,value:m}=u.target;c(g=>({...g,[s]:s.includes("_amount")?m===""?0:Number(m):m}))},h=async u=>{if(u.preventDefault(),!a?.id){l.error("Gagal menyimpan: Konfigurasi belum dimuat.");return}x(!0);try{await i(a.id,{pemuda_amount:n.pemuda_amount||0,pemudi_amount:n.pemudi_amount||0,pelajar_amount:n.pelajar_amount||0,notes:n.notes||""}),l.success("Konfigurasi iuran berhasil disimpan ke database!")}catch(s){console.error("Error saving dues configuration:",s),l.error("Gagal menyimpan konfigurasi ke database")}finally{x(!1)}};return o?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(f,{text:"Memuat konfigurasi dari database..."})}):e.jsxs("div",{className:"space-y-6",children:[e.jsx(y,{title:"Pengaturan Iuran Anggota"}),!a&&e.jsxs("div",{className:"bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4",role:"alert",children:[e.jsx("p",{className:"font-bold",children:"Info"}),e.jsx("p",{children:"Membuat konfigurasi default. Silakan sesuaikan nilai iuran sesuai kebutuhan."})]}),e.jsxs("form",{onSubmit:h,className:"bg-white p-6 rounded-lg shadow space-y-4 max-w-2xl",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"pemuda_amount",className:"block text-sm font-medium text-gray-700 mb-1",children:"Iuran Pemuda (Rp)"}),e.jsx(d,{id:"pemuda_amount",name:"pemuda_amount",type:"number",value:n.pemuda_amount||"",onChange:t,placeholder:"Contoh: 250000",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"pemudi_amount",className:"block text-sm font-medium text-gray-700 mb-1",children:"Iuran Pemudi (Rp)"}),e.jsx(d,{id:"pemudi_amount",name:"pemudi_amount",type:"number",value:n.pemudi_amount||"",onChange:t,placeholder:"Contoh: 150000",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"pelajar_amount",className:"block text-sm font-medium text-gray-700 mb-1",children:"Iuran Pelajar (Rp)"}),e.jsx(d,{id:"pelajar_amount",name:"pelajar_amount",type:"number",value:n.pelajar_amount||"",onChange:t,placeholder:"Contoh: 150000",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"notes",className:"block text-sm font-medium text-gray-700 mb-1",children:"Catatan Tambahan"}),e.jsx(b,{id:"notes",name:"notes",rows:4,value:n.notes||"",onChange:t,placeholder:"Tambahkan catatan mengenai iuran jika ada..."})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx(v,{type:"submit",disabled:p,children:p?e.jsxs("span",{className:"flex items-center",children:[e.jsx(f,{size:"small"}),e.jsx("span",{className:"ml-2",children:"Menyimpan..."})]}):e.jsxs("span",{className:"flex items-center",children:[e.jsx(N,{size:16,className:"mr-2"})," Simpan Perubahan"]})})})]})]})};export{I as default};
