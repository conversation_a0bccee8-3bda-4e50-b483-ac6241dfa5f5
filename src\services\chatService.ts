import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  serverTimestamp,
  arrayUnion,
  arrayRemove,
  writeBatch,
  getDocs,
  getDoc
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { db, storage } from '../lib/firebase';
import { User, ChatRoom, Message, MessageReaction, TypingIndicator } from '../types/chat';

export class ChatService {
  // User Management
  static async createOrUpdateUser(userData: User) {
    const userRef = doc(db, 'users', userData.id);
    await updateDoc(userRef, {
      ...userData,
      lastSeen: serverTimestamp()
    }).catch(async () => {
      // If document doesn't exist, create it
      await addDoc(collection(db, 'users'), {
        ...userData,
        lastSeen: serverTimestamp()
      });
    });
  }

  static async updateUserOnlineStatus(userId: string, isOnline: boolean) {
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      isOnline,
      lastSeen: serverTimestamp()
    });
  }

  static subscribeToOnlineUsers(callback: (users: User[]) => void) {
    const q = query(collection(db, 'users'), where('isOnline', '==', true));
    return onSnapshot(q, (snapshot) => {
      const users = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as User[];
      callback(users);
    });
  }

  // Chat Rooms
  static async createChatRoom(room: Omit<ChatRoom, 'id' | 'createdAt'>) {
    const roomRef = await addDoc(collection(db, 'chatRooms'), {
      ...room,
      createdAt: serverTimestamp()
    });
    return roomRef.id;
  }

  static async getGeneralChatRoom(): Promise<ChatRoom | null> {
    const q = query(
      collection(db, 'chatRooms'),
      where('type', '==', 'general'),
      limit(1)
    );
    const snapshot = await getDocs(q);
    if (snapshot.empty) return null;
    
    const doc = snapshot.docs[0];
    return { id: doc.id, ...doc.data() } as ChatRoom;
  }

  static async createPrivateRoom(userId1: string, userId2: string): Promise<string> {
    // Check if private room already exists
    const q = query(
      collection(db, 'chatRooms'),
      where('type', '==', 'private'),
      where('participants', 'array-contains', userId1)
    );
    
    const snapshot = await getDocs(q);
    const existingRoom = snapshot.docs.find(doc => {
      const data = doc.data();
      return data.participants.includes(userId2);
    });

    if (existingRoom) {
      return existingRoom.id;
    }

    // Create new private room
    const roomRef = await addDoc(collection(db, 'chatRooms'), {
      name: 'Private Chat',
      type: 'private',
      participants: [userId1, userId2],
      createdBy: userId1,
      createdAt: serverTimestamp(),
      isActive: true
    });

    return roomRef.id;
  }

  static subscribeToUserChatRooms(userId: string, callback: (rooms: ChatRoom[]) => void) {
    const q = query(
      collection(db, 'chatRooms'),
      where('participants', 'array-contains', userId),
      where('isActive', '==', true),
      orderBy('createdAt', 'desc')
    );

    return onSnapshot(q, (snapshot) => {
      const rooms = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ChatRoom[];
      callback(rooms);
    });
  }

  // Messages
  static async sendMessage(message: Omit<Message, 'id' | 'timestamp' | 'reactions' | 'isEdited'>) {
    const batch = writeBatch(db);

    // Add message
    const messageRef = doc(collection(db, 'messages'));
    batch.set(messageRef, {
      ...message,
      timestamp: serverTimestamp(),
      reactions: [],
      isEdited: false
    });

    // Update room's last message
    const roomRef = doc(db, 'chatRooms', message.roomId);
    batch.update(roomRef, {
      lastMessage: {
        id: messageRef.id,
        content: message.content,
        senderId: message.senderId,
        timestamp: serverTimestamp()
      }
    });

    await batch.commit();
    return messageRef.id;
  }

  static subscribeToRoomMessages(roomId: string, callback: (messages: Message[]) => void) {
    const q = query(
      collection(db, 'messages'),
      where('roomId', '==', roomId),
      orderBy('timestamp', 'asc'),
      limit(100)
    );

    return onSnapshot(q, (snapshot) => {
      const messages = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate()
      })) as Message[];
      callback(messages);
    });
  }

  // File Upload
  static async uploadFile(file: File, roomId: string): Promise<{ url: string; fileName: string; fileSize: number }> {
    const fileName = `${Date.now()}_${file.name}`;
    const storageRef = ref(storage, `chat/${roomId}/${fileName}`);
    
    await uploadBytes(storageRef, file);
    const url = await getDownloadURL(storageRef);
    
    return {
      url,
      fileName: file.name,
      fileSize: file.size
    };
  }

  // Message Reactions
  static async addReaction(messageId: string, userId: string, emoji: string) {
    const messageRef = doc(db, 'messages', messageId);
    const reaction: MessageReaction = {
      userId,
      emoji,
      timestamp: new Date()
    };

    await updateDoc(messageRef, {
      reactions: arrayUnion(reaction)
    });
  }

  static async removeReaction(messageId: string, userId: string, emoji: string) {
    const messageRef = doc(db, 'messages', messageId);
    const messageDoc = await getDoc(messageRef);
    
    if (messageDoc.exists()) {
      const data = messageDoc.data();
      const reactions = data.reactions.filter((r: MessageReaction) => 
        !(r.userId === userId && r.emoji === emoji)
      );
      
      await updateDoc(messageRef, { reactions });
    }
  }

  // Typing Indicators
  static async setTyping(roomId: string, userId: string, userName: string, isTyping: boolean) {
    const typingRef = doc(db, 'typing', roomId, 'users', userId);
    
    if (isTyping) {
      await updateDoc(typingRef, {
        userId,
        userName,
        roomId,
        timestamp: serverTimestamp()
      });
    } else {
      await deleteDoc(typingRef);
    }
  }

  static subscribeToTyping(roomId: string, callback: (typing: TypingIndicator[]) => void) {
    const q = collection(db, 'typing', roomId, 'users');
    
    return onSnapshot(q, (snapshot) => {
      const typing = snapshot.docs.map(doc => ({
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate()
      })) as TypingIndicator[];
      callback(typing);
    });
  }
}
