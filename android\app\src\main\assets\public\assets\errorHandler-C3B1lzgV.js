import{J as f}from"./index-F6RtwXXX.js";const g=a=>{const e=a;if(e.code?.startsWith("auth/"))switch(e.code){case"auth/user-not-found":return{code:e.code,message:e.message||"User not found",userMessage:"Email tidak terdaftar",shouldRetry:!1};case"auth/wrong-password":return{code:e.code,message:e.message||"Wrong password",userMessage:"Password salah",shouldRetry:!1};case"auth/network-request-failed":return{code:e.code,message:e.message||"Network request failed",userMessage:"Koneksi internet bermasalah. Coba lagi.",shouldRetry:!0};default:return{code:e.code,message:e.message||"Auth error",userMessage:"Gagal login. Coba lagi.",shouldRetry:!0}}if(e.code?.startsWith("firestore/"))switch(e.code){case"firestore/permission-denied":return{code:e.code,message:e.message||"Permission denied",userMessage:"Aks<PERSON> ditolak. Login ulang.",shouldRetry:!1};case"firestore/unavailable":return{code:e.code,message:e.message||"Service unavailable",userMessage:"Server sedang bermasalah. Coba lagi.",shouldRetry:!0};case"firestore/deadline-exceeded":return{code:e.code,message:e.message||"Deadline exceeded",userMessage:"Koneksi timeout. Coba lagi.",shouldRetry:!0};default:return{code:e.code,message:e.message||"Firestore error",userMessage:"Gagal menyimpan data. Coba lagi.",shouldRetry:!0}}if(e.message?.includes("VALIDASI GAGAL"))return{code:"validation-error",message:e.message,userMessage:e.message.replace("❌ VALIDASI GAGAL: ",""),shouldRetry:!1};if(e.message?.includes("network")||e.message?.includes("fetch")||e.message?.includes("timeout")||e.message?.includes("koneksi"))return{code:"network-error",message:e.message,userMessage:"Koneksi internet bermasalah. Periksa koneksi Anda.",shouldRetry:!0};if(e.code?.startsWith("api/")||e.message?.includes("status code")){const s=e.message?.match(/status code (\d+)/),r=s?s[1]:"";return r==="401"||r==="403"?{code:`api/unauthorized-${r}`,message:e.message||`API error ${r}`,userMessage:"Sesi Anda telah berakhir. Silakan login kembali.",shouldRetry:!1}:r==="404"?{code:"api/not-found",message:e.message||"Resource not found",userMessage:"Data yang Anda cari tidak ditemukan.",shouldRetry:!1}:r==="429"?{code:"api/rate-limited",message:e.message||"Too many requests",userMessage:"Terlalu banyak permintaan. Coba lagi setelah beberapa saat.",shouldRetry:!0}:r?.startsWith("5")?{code:"api/server-error",message:e.message||"Server error",userMessage:"Server sedang bermasalah. Coba lagi nanti.",shouldRetry:!0}:{code:"api/error",message:e.message||"API error",userMessage:"Terjadi kesalahan saat memproses permintaan. Coba lagi.",shouldRetry:!0}}return{code:"unknown-error",message:e.message||"Unknown error",userMessage:"Terjadi kesalahan. Coba lagi atau hubungi admin.",shouldRetry:!0}},k=(a,e)=>{const s=g(a);return console.error(`❌ ERROR in ${e||"Unknown"}:`,{code:s.code,message:s.message,userMessage:s.userMessage,shouldRetry:s.shouldRetry,timestamp:new Date().toISOString()}),f.error(s.userMessage,{duration:s.shouldRetry?5e3:3e3,action:s.shouldRetry?{label:"Coba Lagi",onClick:()=>{}}:void 0}),s},b=async(a,e=3,s=1e3,r)=>{let u;const o=r||"unknown operation",d=Date.now();for(let t=1;t<=e;t++)try{const n=await a();return t>1&&console.warn(`✅ ${o} succeeded after ${t} attempts in ${Date.now()-d}ms`),n}catch(n){u=n;const i=g(n),m=Math.random()*.3+.85,l=Math.min(s*Math.pow(2,t-1)*m,3e4);if(console.warn(`⚠️ Attempt ${t}/${e} failed in ${o}:`,{message:i.userMessage,retryable:i.shouldRetry,nextRetryIn:t<e?`${Math.round(l)}ms`:"N/A",elapsedTime:`${Date.now()-d}ms`}),!i.shouldRetry)throw console.error(`❌ ${o} failed with non-retryable error:`,i.message),n;if(t===e){console.error(`❌ ${o} failed after ${e} attempts in ${Date.now()-d}ms`);break}c()||(console.warn(`⏳ Waiting for network in ${o}...`),await w(),console.warn(`🔄 Network connection restored, continuing with ${o}`)),await new Promise(h=>setTimeout(h,l))}throw u},c=()=>navigator.onLine,w=()=>new Promise(a=>{if(c()){a();return}const e=()=>{window.removeEventListener("online",e),a()};window.addEventListener("online",e)});export{k as h,b as w};
