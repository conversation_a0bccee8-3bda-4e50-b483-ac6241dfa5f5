import{r as t,m as B,n as y,k as e,o as u,d as v,U as w}from"./index-BmrqMJCI.js";import{A as D}from"./arrow-left-Ds-WssMx.js";import{L as N,E,a as k,A as _}from"./lock-B4AwFXUp.js";const C=()=>{const[r,n]=t.useState(""),[o,b]=t.useState(""),[d,l]=t.useState(""),[a,h]=t.useState(!1),[m,c]=t.useState(!1),x=B(),{login:f,isLoading:p}=y();t.useEffect(()=>{const s=localStorage.getItem("rememberedEmail");s&&(n(s),c(!0))},[]);const g=async s=>{s.preventDefault(),l("");try{await f(r,o)?(m?localStorage.setItem("rememberedEmail",r):localStorage.removeItem("rememberedEmail"),x("/admin")):l("Login gagal. Periksa email dan password Anda.")}catch(i){const j=i instanceof Error?i.message:"Login gagal. Periksa email dan password Anda.";l(j)}};return e.jsxs("div",{className:"bg-[#F9F9F9] min-h-screen flex items-center justify-center p-4 sm:p-6",children:[e.jsxs(u.button,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3},onClick:()=>x("/"),className:"fixed top-4 left-4 sm:top-6 sm:left-6 z-10 flex items-center space-x-2 px-3 py-2 sm:px-4 sm:py-2 bg-white border-2 border-[#5D534B] rounded-full shadow-[4px_4px_0px_#5D534B] hover:shadow-[2px_2px_0px_#5D534B] hover:translate-x-[2px] hover:translate-y-[2px] transition-all duration-150 text-[#5D534B] font-medium text-sm",children:[e.jsx(D,{size:16}),e.jsx("span",{className:"hidden sm:inline",children:"Kembali ke Beranda"}),e.jsx("span",{className:"sm:hidden",children:"Beranda"})]}),e.jsxs(u.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md bg-white p-6 sm:p-8 rounded-2xl border-2 sm:border-4 border-[#5D534B] shadow-[6px_6px_0px_#5D534B] sm:shadow-[8px_8px_0px_#5D534B]",children:[e.jsxs("div",{className:"flex flex-col items-center mb-6",children:[e.jsx("div",{className:"w-16 h-16 bg-[#FCE09B] rounded-full border-4 border-[#5D534B] flex items-center justify-center mb-4",children:e.jsx(v,{size:32,className:"text-[#5D534B]"})}),e.jsx("h1",{className:"text-xl sm:text-2xl font-bold text-[#5D534B]",children:"Admin Login"}),e.jsx("p",{className:"text-[#5D534B]/80 text-sm text-center",children:"Masuk untuk mengelola aplikasi DANAPEMUDA"})]}),d&&e.jsx("div",{className:"bg-[#FF9898]/30 border-2 border-[#FF9898] p-3 rounded-lg mb-4 text-[#5D534B] text-sm text-center",children:d}),e.jsxs("form",{onSubmit:g,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium mb-1 text-[#5D534B]",children:"Email"}),e.jsxs("div",{className:"relative",children:[e.jsx(w,{size:18,className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#5D534B]/70"}),e.jsx("input",{id:"email",type:"email",value:r,onChange:s=>n(s.target.value),placeholder:"<EMAIL>",className:"w-full pl-10 pr-4 py-2 border-2 border-[#5D534B] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FCE09B] text-sm sm:text-base",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium mb-1 text-[#5D534B]",children:"Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(N,{size:18,className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#5D534B]/70"}),e.jsx("input",{id:"password",type:a?"text":"password",value:o,onChange:s=>b(s.target.value),placeholder:"Masukkan password",className:"w-full pl-10 pr-10 py-2 border-2 border-[#5D534B] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FCE09B] text-sm sm:text-base",required:!0}),e.jsx("button",{type:"button",onClick:()=>h(!a),className:"absolute right-3 top-1/2 -translate-y-1/2 text-[#5D534B]/70 hover:text-[#5D534B] p-1","aria-label":a?"Sembunyikan sandi":"Tampilkan sandi",children:a?e.jsx(E,{size:18}):e.jsx(k,{size:18})})]})]}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",checked:m,onChange:s=>c(s.target.checked),className:"h-4 w-4 text-[#FCE09B] focus:ring-[#f9d572] border-[#5D534B] rounded"}),e.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-xs sm:text-sm text-[#5D534B]",children:"Ingat Saya"})]})}),e.jsx("button",{type:"submit",disabled:p,className:"w-full bg-[#FCE09B] text-[#5D534B] font-bold py-2.5 sm:py-3 px-4 rounded-full border-2 sm:border-4 border-[#5D534B] hover:bg-[#ffd166] focus:outline-none focus:ring-2 focus:ring-[#FCE09B] focus:ring-opacity-50 shadow-[4px_4px_0px_#5D534B] flex items-center justify-center gap-2 disabled:opacity-70 active:shadow-[2px_2px_0px_#5D534B] active:translate-x-[2px] active:translate-y-[2px] transition-all duration-150",children:p?e.jsx("div",{className:"w-5 h-5 border-2 border-[#5D534B] border-t-transparent rounded-full animate-spin"}):e.jsxs("span",{className:"flex items-center gap-1",children:["Masuk ",e.jsx(_,{size:18})]})})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsx("p",{className:"text-xs text-[#5D534B]/60",children:"© 2023 DANAPEMUDA. All rights reserved."})})]})]})};export{C as default};
