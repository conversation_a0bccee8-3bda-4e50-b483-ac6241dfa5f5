import React from 'react';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { formatDate } from '../../utils/formatters';

interface Activity {
  type: 'member' | 'expense' | 'event';
  title: string;
  date: string;
  icon: LucideIcon;
}

interface RecentActivitiesProps {
  activities: Activity[];
  isLoading: boolean;
}

const RecentActivities: React.FC<RecentActivitiesProps> = ({ activities, isLoading }) => {
  if (isLoading) {
    return (
      <Card className="neo-card">
        <CardHeader>
          <CardTitle className="text-[#5D534B] text-lg sm:text-xl">Aktivitas Terbaru</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="neo-card">
      <CardHeader>
        <CardTitle className="text-[#5D534B] text-lg sm:text-xl">Aktivitas Terbaru</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 sm:space-y-4">
          {activities.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p className="text-sm sm:text-base">Belum ada aktivitas terbaru</p>
            </div>
          ) : (
            activities.map((activity, index) => (
              <motion.div
                key={`${activity.type}-${index}`}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-[#F3E5F5] flex items-center justify-center">
                    <activity.icon 
                      size={16} 
                      className="text-[#B39DDB] sm:w-5 sm:h-5" 
                    />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm sm:text-base text-[#5D534B] font-medium leading-tight">
                    {activity.title}
                  </p>
                  <p className="text-xs sm:text-sm text-gray-500 mt-1">
                    {formatDate(activity.date)}
                  </p>
                </div>
              </motion.div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default RecentActivities;
