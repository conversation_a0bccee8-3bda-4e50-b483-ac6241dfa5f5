import { Capacitor } from '@capacitor/core';
import { auth, db } from '../lib/firebase';
import { 
  signInWithEmailAndPassword as webSignIn,
  signOut as webSignOut,
  onAuthStateChanged,
  User
} from 'firebase/auth';
import {
  enableNetwork,
  disableNetwork
} from 'firebase/firestore';

export class MobileFirebaseService {
  private static initialized = false;

  static async initializeApp() {
    if (this.initialized) return;

    try {
      if (Capacitor.isNativePlatform()) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('Initializing Firebase for mobile platform');
        }
        
        // Enable offline persistence for mobile
        await this.setupOfflineSupport();
        
        // Setup network monitoring
        await this.setupNetworkMonitoring();
        
        this.initialized = true;
        if (process.env.NODE_ENV === 'development') {
          console.warn('Mobile Firebase initialized successfully');
        }
      }
    } catch (error) {
      console.error('Error initializing mobile Firebase:', error);
    }
  }

  static async setupOfflineSupport() {
    try {
      // Enable offline persistence
      if (process.env.NODE_ENV === 'development') {
        console.warn('Setting up offline support...');
      }
      
      // Handle network state changes
      window.addEventListener('online', async () => {
        if (process.env.NODE_ENV === 'development') {
          console.warn('Network online - enabling Firestore');
        }
        try {
          await enableNetwork(db);
        } catch (error) {
          console.error('Error enabling network:', error);
        }
      });

      window.addEventListener('offline', async () => {
        if (process.env.NODE_ENV === 'development') {
          console.warn('Network offline - disabling Firestore');
        }
        try {
          await disableNetwork(db);
        } catch (error) {
          console.error('Error disabling network:', error);
        }
      });
    } catch (error) {
      console.error('Error setting up offline support:', error);
    }
  }

  static async setupNetworkMonitoring() {
    if (Capacitor.isNativePlatform()) {
      try {
        const { Network } = await import('@capacitor/network');
        
        // Check initial network status
        const status = await Network.getStatus();
        if (process.env.NODE_ENV === 'development') {
          console.warn('Initial network status:', status);
        }

        // Listen for network changes
        Network.addListener('networkStatusChange', async (status) => {
          if (process.env.NODE_ENV === 'development') {
            console.warn('Network status changed:', status);
          }
          
          if (status.connected) {
            await enableNetwork(db);
          } else {
            await disableNetwork(db);
          }
        });
      } catch (error) {
        console.error('Error setting up network monitoring:', error);
      }
    }
  }

  static async signInWithEmailAndPassword(email: string, password: string) {
    try {
      // Use web Firebase for authentication (works on mobile too)
      const result = await webSignIn(auth, email, password);
      if (process.env.NODE_ENV === 'development') {
        console.warn('Mobile sign in successful:', result.user.uid);
      }
      return result;
    } catch (error) {
      console.error('Mobile sign in error:', error);
      throw error;
    }
  }

  static async signOut() {
    try {
      await webSignOut(auth);
      if (process.env.NODE_ENV === 'development') {
        console.warn('Mobile sign out successful');
      }
    } catch (error) {
      console.error('Mobile sign out error:', error);
      throw error;
    }
  }

  static onAuthStateChanged(callback: (user: User | null) => void) {
    return onAuthStateChanged(auth, callback);
  }

  static getCurrentUser() {
    return auth.currentUser;
  }

  static async getDeviceInfo() {
    if (Capacitor.isNativePlatform()) {
      try {
        const { Device } = await import('@capacitor/device');
        return await Device.getInfo();
      } catch (error) {
        console.error('Error getting device info:', error);
        return null;
      }
    }
    return null;
  }
}

// Auto-initialize when imported
if (Capacitor.isNativePlatform()) {
  MobileFirebaseService.initializeApp();
}
