import { Capacitor } from '@capacitor/core';

// Extend CSSStyleDeclaration to include webkit properties
declare global {
  interface CSSStyleDeclaration {
    webkitOverflowScrolling?: string;
    overflowScrolling?: string;
  }
}

export class MobileOptimizations {
  private static initialized = false;

  static initializeAll() {
    if (this.initialized || !Capacitor.isNativePlatform()) return;

    if (process.env.NODE_ENV === 'development') {
      console.warn('Initializing mobile optimizations...');
    }
    
    this.enableFastClick();
    this.optimizeScrolling();
    this.preventZoom();
    this.optimizeViewport();
    this.setupTouchOptimizations();
    
    this.initialized = true;
    if (process.env.NODE_ENV === 'development') {
      console.warn('Mobile optimizations initialized');
    }
  }

  static enableFastClick() {
    if (Capacitor.isNativePlatform()) {
      // Disable 300ms click delay on mobile
      document.addEventListener('touchstart', () => {}, { passive: true });
      
      // Add fast-click class to body
      document.body.classList.add('mobile-app');
      
      if (process.env.NODE_ENV === 'development') {
        console.warn('Fast click enabled');
      }
    }
  }

  static optimizeScrolling() {
    if (Capacitor.isNativePlatform()) {
      // Enable momentum scrolling
      document.body.style.webkitOverflowScrolling = 'touch';
      document.body.style.overflowScrolling = 'touch';
      
      // Prevent scroll bounce
      document.body.style.overscrollBehavior = 'none';
      
      if (process.env.NODE_ENV === 'development') {
        console.warn('Scrolling optimized');
      }
    }
  }

  static preventZoom() {
    if (Capacitor.isNativePlatform()) {
      // Prevent zoom on input focus
      const viewport = document.querySelector('meta[name=viewport]');
      if (viewport) {
        viewport.setAttribute('content', 
          'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
        );
      }
      
      if (process.env.NODE_ENV === 'development') {
        console.warn('Zoom prevention enabled');
      }
    }
  }

  static optimizeViewport() {
    if (Capacitor.isNativePlatform()) {
      // Set viewport for mobile app
      let viewport = document.querySelector('meta[name=viewport]');
      
      if (!viewport) {
        viewport = document.createElement('meta');
        viewport.setAttribute('name', 'viewport');
        document.head.appendChild(viewport);
      }
      
      viewport.setAttribute('content', 
        'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
      );
      
      if (process.env.NODE_ENV === 'development') {
        console.warn('Viewport optimized');
      }
    }
  }

  static setupTouchOptimizations() {
    if (Capacitor.isNativePlatform()) {
      // Improve touch responsiveness
      document.addEventListener('touchstart', (e) => {
        // Add touch feedback
        const target = e.target as HTMLElement;
        if (target.classList.contains('neo-button') || target.closest('.neo-button')) {
          target.style.transform = 'scale(0.98)';
          setTimeout(() => {
            target.style.transform = '';
          }, 100);
        }
      }, { passive: true });

      // Prevent text selection on touch
      document.body.style.webkitUserSelect = 'none';
      document.body.style.userSelect = 'none';
      
      if (process.env.NODE_ENV === 'development') {
        console.warn('Touch optimizations enabled');
      }
    }
  }

  static measurePageLoad(pageName: string) {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const loadTime = endTime - startTime;
      
      if (process.env.NODE_ENV === 'development') {
        console.warn(`📱 ${pageName} loaded in ${loadTime.toFixed(2)}ms`);
      }
      
      // Log slow pages
      if (loadTime > 3000) {
        console.warn(`⚠️ Slow page load: ${pageName} took ${loadTime}ms`);
      }
      
      return loadTime;
    };
  }

  static enableHapticFeedback() {
    if (Capacitor.isNativePlatform()) {
      import('@capacitor/haptics').then(({ Haptics, ImpactStyle }) => {
        // Add haptic feedback to buttons
        document.addEventListener('click', (e) => {
          const target = e.target as HTMLElement;
          if (target.classList.contains('neo-button') || target.closest('.neo-button')) {
            Haptics.impact({ style: ImpactStyle.Light });
          }
        });
        
        if (process.env.NODE_ENV === 'development') {
          console.warn('Haptic feedback enabled');
        }
      }).catch(error => {
        console.error('Error enabling haptic feedback:', error);
      });
    }
  }
}

// Auto-initialize when imported on mobile
if (Capacitor.isNativePlatform()) {
  // Initialize after DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      MobileOptimizations.initializeAll();
    });
  } else {
    MobileOptimizations.initializeAll();
  }
}
