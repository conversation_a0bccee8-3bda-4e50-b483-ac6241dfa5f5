import { useState, useEffect } from 'react';
import { Input } from '../components/ui/input';
import { Button } from '../components/ui/button';
import MemberFormModal from '../components/MemberFormModal';
import { MemberData } from '../hooks/useMembers';
import { Loader2 } from 'lucide-react';

type MemberFormData = Omit<MemberData, 'id'>;

export default function AdminMembersPage() {
  const [members, setMembers] = useState<MemberData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [selectedMember, setSelectedMember] = useState<MemberData | undefined>();
  const [toggleLoading, setToggleLoading] = useState<string | null>(null);

  const loadMembers = async () => {
    try {
      setLoading(true);
      // TODO: Implement Firebase data loading
      // const data = await getMembersFromFirebase();
      // setMembers(data);
      setMembers([]); // Temporary empty array
    } catch (error) {
      console.error('Error loading members:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMembers();
  }, []);

  const handleAdd = async (_member: MemberFormData) => {
    try {
      setLoading(true);
      // TODO: Implement Firebase create member
      // await createMemberInFirebase(_member);
      await loadMembers();
      setShowModal(false);
    } catch (error) {
      console.error('Error adding member:', error);
      alert('Gagal menambah anggota: ' + (error instanceof Error ? error.message : 'Terjadi kesalahan'));
    } finally {
      setLoading(false);
    }
  };

  const handleEditMember = async (_member: MemberFormData) => {
    if (!selectedMember?.id) return;
    try {
      setLoading(true);
      // TODO: Implement Firebase update member
      // await updateMemberInFirebase(selectedMember.id, _member);
      await loadMembers();
      setShowModal(false);
      setSelectedMember(undefined);
    } catch (error) {
      console.error('Error editing member:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteMember = async (_id: string) => {
    try {
      setLoading(true);
      // TODO: Implement Firebase delete member
      // await deleteMemberFromFirebase(_id);
      await loadMembers();
    } catch (error) {
      console.error('Error deleting member:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async (member: MemberData) => {
    if (toggleLoading === member.id) return; // Prevent multiple clicks

    try {
      setToggleLoading(member.id);

      // TODO: Implement Firebase update member status
      // const newStatus = member.payment_status === 'paid' ? 'unpaid' : 'paid';
      // const updatedMember = {
      //   ...member,
      //   payment_status: newStatus,
      //   payment_date: newStatus === 'paid' ? new Date().toISOString().split('T')[0] : null
      // };
      // await updateMemberInFirebase(member.id, updatedMember);

      // Refresh data
      await loadMembers();
    } catch (error) {
      console.error('Error toggling payment status:', error);
      alert('Gagal mengubah status: ' + (error instanceof Error ? error.message : 'Terjadi kesalahan'));
    } finally {
      setToggleLoading(null);
    }
  };

  const filteredMembers = members.filter(member =>
    member.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Daftar Anggota</h1>
        <Button onClick={() => setShowModal(true)}>Tambah Anggota</Button>
      </div>

      <div className="mb-4">
        <Input
          type="text"
          placeholder="Cari anggota..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-sm"
        />
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-gray-100">
                <th className="p-4 text-left">Nama</th>
                <th className="p-4 text-left">Status Pembayaran</th>
                <th className="p-4 text-left">Jumlah Pembayaran</th>
                <th className="p-4 text-left">Tanggal Pembayaran</th>
                <th className="p-4 text-left">Aksi</th>
              </tr>
            </thead>
            <tbody>
              {filteredMembers.map((member) => (
                <tr key={member.id} className="border-b">
                  <td className="p-4">{member.name}</td>
                  <td className="p-4">
                    <button
                      type="button"
                      onClick={() => handleToggleStatus(member)}
                      disabled={toggleLoading === member.id}
                      className={`px-3 py-1 rounded-full text-sm font-medium cursor-pointer transition-colors duration-200 ${
                        member.payment_status === 'paid'
                          ? 'bg-green-100 text-green-800 hover:bg-green-200'
                          : 'bg-red-100 text-red-800 hover:bg-red-200'
                      }`}
                    >
                      {toggleLoading === member.id ? (
                        <Loader2 className="h-4 w-4 animate-spin inline mr-1" />
                      ) : null}
                      {member.payment_status === 'paid' ? 'Lunas' : 'Belum Lunas'}
                    </button>
                  </td>
                  <td className="p-4">Rp {member.payment_amount.toLocaleString('id-ID')}</td>
                  <td className="p-4">
                    {member.payment_date ? new Date(member.payment_date).toLocaleDateString('id-ID') : '-'}
                  </td>
                  <td className="p-4">
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedMember(member);
                          setShowModal(true);
                        }}
                      >
                        Edit
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeleteMember(member.id)}
                      >
                        Hapus
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <MemberFormModal
        isOpen={showModal}
        onClose={() => {
          setShowModal(false);
          setSelectedMember(undefined);
        }}
        onSubmit={selectedMember ? handleEditMember : handleAdd}
        member={selectedMember}
      />
    </div>
  );
} 