import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { TypingIndicator as TypingIndicatorType } from '../../types/chat';

interface TypingIndicatorProps {
  users: TypingIndicatorType[];
}

const TypingIndicator: React.FC<TypingIndicatorProps> = ({ users }) => {
  if (users.length === 0) return null;

  const getTypingText = () => {
    if (users.length === 1) {
      return `${users[0].userName} is typing...`;
    } else if (users.length === 2) {
      return `${users[0].userName} and ${users[1].userName} are typing...`;
    } else {
      return `${users[0].userName} and ${users.length - 1} others are typing...`;
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 10 }}
        className="flex items-center space-x-3 p-3"
      >
        {/* Avatar */}
        <div className="w-8 h-8 bg-[#FCE09B] border-2 border-[#5D534B] rounded-full flex items-center justify-center">
          <span className="text-sm font-bold text-[#5D534B]">
            {users[0].userName.charAt(0).toUpperCase()}
          </span>
        </div>

        {/* Typing Message */}
        <div className="bg-white border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] rounded-lg p-3 flex items-center space-x-2">
          <span className="text-sm text-[#5D534B]/70 italic">
            {getTypingText()}
          </span>
          
          {/* Animated Dots */}
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: i * 0.2
                }}
                className="w-2 h-2 bg-[#5D534B] rounded-full"
              />
            ))}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default TypingIndicator;
