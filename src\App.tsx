import { Browser<PERSON>outer } from "react-router-dom";
import { Toaster } from "sonner";
import { AuthProvider } from "@/context/AuthContext";
import { MembersProvider } from "@/context/MembersContext";
import { EventsProvider } from "@/context/EventsContext";
import { ExpensesProvider } from "@/context/ExpensesContext";
import { DuesConfigProvider } from "@/context/DuesConfigContext";
import AnimatedRoutes from "./components/AnimatedRoutes";
import FirebaseErrorBoundary from "./components/FirebaseErrorBoundary";
import MobileAppLayout from "./components/mobile/MobileAppLayout";

const App = () => (
  <>
    <Toaster position="top-right" />
    <FirebaseErrorBoundary>
      <AuthProvider>
        <MobileAppLayout>
          <MembersProvider>
            <EventsProvider>
              <ExpensesProvider>
                <DuesConfigProvider>
                  <BrowserRouter>
                    <AnimatedRoutes />
                  </BrowserRouter>
                </DuesConfigProvider>
              </ExpensesProvider>
            </EventsProvider>
          </MembersProvider>
        </MobileAppLayout>
      </AuthProvider>
    </FirebaseErrorBoundary>
  </>
);

export default App;
