@echo off
echo.
echo ========================================
echo   DANAPEMUDA Mobile Development Helper
echo ========================================
echo.

:menu
echo Choose an option:
echo.
echo 1. Check device connection
echo 2. Build and run on device
echo 3. Open Android Studio
echo 4. Build for production
echo 5. Clean and rebuild
echo 6. Check Capacitor status
echo 7. Exit
echo.
set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto check_device
if "%choice%"=="2" goto run_device
if "%choice%"=="3" goto open_studio
if "%choice%"=="4" goto build_prod
if "%choice%"=="5" goto clean_build
if "%choice%"=="6" goto check_status
if "%choice%"=="7" goto exit
goto menu

:check_device
echo.
echo Checking connected devices...
adb devices
echo.
pause
goto menu

:run_device
echo.
echo Building and running on device...
npm run android:dev
echo.
pause
goto menu

:open_studio
echo.
echo Opening Android Studio...
npm run android:open
echo.
pause
goto menu

:build_prod
echo.
echo Building for production...
npm run android:build
echo.
pause
goto menu

:clean_build
echo.
echo Cleaning and rebuilding...
cd android
gradlew clean
cd ..
npm run build:mobile
npx cap sync android
echo.
echo Clean build completed!
pause
goto menu

:check_status
echo.
echo Checking Capacitor status...
npx cap doctor android
echo.
pause
goto menu

:exit
echo.
echo Goodbye!
exit
