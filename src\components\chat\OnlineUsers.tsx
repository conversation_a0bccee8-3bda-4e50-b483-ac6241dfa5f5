import React from 'react';
import { motion } from 'framer-motion';
import { MessageSquare, Crown, User, Circle } from 'lucide-react';
import { useChatContext } from '../../context/ChatContext';
import { useAuth } from '../../context/AuthContext';

const OnlineUsers: React.FC = () => {
  const { onlineUsers, createPrivateChat } = useChatContext();
  const { user } = useAuth();

  const handleStartPrivateChat = async (userId: string) => {
    if (userId === user?.uid) return;
    
    try {
      await createPrivateChat(userId);
    } catch (error) {
      console.error('Error starting private chat:', error);
    }
  };

  const getStatusColor = (isOnline: boolean) => {
    return isOnline ? 'text-green-500' : 'text-gray-400';
  };

  const formatLastSeen = (lastSeen: Date) => {
    const now = new Date();
    const diff = now.getTime() - lastSeen.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    
    return new Intl.DateTimeFormat('id-ID', {
      day: 'numeric',
      month: 'short'
    }).format(lastSeen);
  };

  // Separate current user and others
  const currentUserData = onlineUsers.find(u => u.id === user?.uid);
  const otherUsers = onlineUsers.filter(u => u.id !== user?.uid);

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b-2 border-[#5D534B] bg-[#FCE09B]">
        <h3 className="font-black text-[#5D534B] flex items-center">
          <User className="w-5 h-5 mr-2" />
          Online Users ({onlineUsers.length})
        </h3>
      </div>

      {/* User List */}
      <div className="flex-1 overflow-y-auto">
        {/* Current User */}
        {currentUserData && (
          <div className="p-4 border-b-2 border-[#5D534B] bg-[#FCE09B]/20">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="w-10 h-10 bg-[#FCE09B] border-2 border-[#5D534B] rounded-full flex items-center justify-center">
                  <span className="font-bold text-[#5D534B]">
                    {currentUserData.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                <Circle className={`absolute -bottom-1 -right-1 w-4 h-4 ${getStatusColor(currentUserData.isOnline)} fill-current`} />
              </div>
              
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <h4 className="font-bold text-[#5D534B]">
                    {currentUserData.name} (You)
                  </h4>
                  {currentUserData.role === 'admin' && (
                    <Crown className="w-4 h-4 text-yellow-600" />
                  )}
                </div>
                <p className="text-sm text-[#5D534B]/70">
                  {currentUserData.isOnline ? 'Online' : formatLastSeen(currentUserData.lastSeen)}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Other Users */}
        <div className="p-2 space-y-2">
          {otherUsers.length === 0 ? (
            <div className="p-4 text-center">
              <User className="w-12 h-12 text-[#5D534B]/50 mx-auto mb-3" />
              <p className="text-[#5D534B]/70 font-medium">No other users online</p>
              <p className="text-sm text-[#5D534B]/50">Check back later!</p>
            </div>
          ) : (
            otherUsers.map((onlineUser) => (
              <motion.button
                key={onlineUser.id}
                onClick={() => handleStartPrivateChat(onlineUser.id)}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="w-full p-3 text-left bg-white border-2 border-[#5D534B] shadow-[2px_2px_0px_#5D534B] hover:shadow-[4px_4px_0px_#5D534B] transition-all rounded"
              >
                <div className="flex items-center space-x-3">
                  {/* Avatar */}
                  <div className="relative">
                    <div className="w-10 h-10 bg-[#9DE0D2] border-2 border-[#5D534B] rounded-full flex items-center justify-center">
                      <span className="font-bold text-[#5D534B]">
                        {onlineUser.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <Circle className={`absolute -bottom-1 -right-1 w-4 h-4 ${getStatusColor(onlineUser.isOnline)} fill-current`} />
                  </div>

                  {/* User Info */}
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-bold text-[#5D534B]">
                        {onlineUser.name}
                      </h4>
                      {onlineUser.role === 'admin' && (
                        <Crown className="w-4 h-4 text-yellow-600" />
                      )}
                    </div>
                    <p className="text-sm text-[#5D534B]/70">
                      {onlineUser.isOnline ? 'Online' : formatLastSeen(onlineUser.lastSeen)}
                    </p>
                  </div>

                  {/* Chat Icon */}
                  <MessageSquare className="w-5 h-5 text-[#5D534B]/50" />
                </div>
              </motion.button>
            ))
          )}
        </div>
      </div>

      {/* Footer Info */}
      <div className="p-4 border-t-2 border-[#5D534B] bg-[#F9F9F9]">
        <p className="text-xs text-[#5D534B]/70 text-center">
          Click on a user to start a private chat
        </p>
      </div>
    </div>
  );
};

export default OnlineUsers;
