import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useAuth } from './AuthContext';
import { ChatService } from '../services/chatService';
import { UserStorageService, ChatUser } from '../services/userStorageService';
import { User, ChatRoom, Message, TypingIndicator } from '../types/chat';

interface ChatContextType {
  // State
  currentRoom: ChatRoom | null;
  rooms: ChatRoom[];
  messages: Message[];
  onlineUsers: User[];
  typingUsers: TypingIndicator[];
  isLoading: boolean;
  currentUser: ChatUser | null;
  showNameInput: boolean;

  // Actions
  setCurrentRoom: (room: ChatRoom | null) => void;
  sendMessage: (content: string, type?: 'text' | 'image', fileData?: { url: string; fileName: string; fileSize?: number }) => Promise<void>;
  createPrivateChat: (userId: string) => Promise<void>;
  addReaction: (messageId: string, emoji: string) => Promise<void>;
  removeReaction: (messageId: string, emoji: string) => Promise<void>;
  setTyping: (isTyping: boolean) => void;
  uploadImage: (file: File) => Promise<void>;
  setUserName: (name: string) => Promise<void>;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export const useChatContext = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChatContext must be used within a ChatProvider');
  }
  return context;
};

interface ChatProviderProps {
  children: React.ReactNode;
}

export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [currentRoom, setCurrentRoom] = useState<ChatRoom | null>(null);
  const [rooms, setRooms] = useState<ChatRoom[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [onlineUsers, setOnlineUsers] = useState<User[]>([]);
  const [typingUsers, setTypingUsers] = useState<TypingIndicator[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState<ChatUser | null>(null);
  const [showNameInput, setShowNameInput] = useState(false);

  // Initialize chat system
  useEffect(() => {
    if (!user) return;

    const initializeChat = async () => {
      try {
        // Create/update user profile in Firestore for chat
        await ChatService.createOrUpdateUser({
          id: user.uid,
          name: user.displayName || 'User',
          isOnline: true,
          lastSeen: new Date(),
          role: 'member' // Default role
        });

        // Set user online
        await ChatService.updateUserOnlineStatus(user.uid, true);

        // Get or create general chat room
        let generalRoom = await ChatService.getGeneralChatRoom();
        if (!generalRoom) {
          const roomId = await ChatService.createChatRoom({
            name: 'DANAPEMUDA Chat',
            type: 'general',
            participants: [],
            createdBy: user.uid,
            isActive: true
          });
          generalRoom = {
            id: roomId,
            name: 'DANAPEMUDA Chat',
            type: 'general',
            participants: [],
            createdBy: user.uid,
            isActive: true,
            createdAt: new Date()
          };
        }

        setCurrentRoom(generalRoom);
        setIsLoading(false);
      } catch (error) {
        console.error('Error initializing chat:', error);
        setIsLoading(false);
      }
    };

    initializeChat();

    // Cleanup on unmount
    return () => {
      if (user) {
        ChatService.updateUserOnlineStatus(user.uid, false);
      }
    };
  }, [user]);

  // Subscribe to user's chat rooms
  useEffect(() => {
    if (!user) return;

    const unsubscribe = ChatService.subscribeToUserChatRooms(user.uid, (userRooms) => {
      setRooms(userRooms);
    });

    return unsubscribe;
  }, [user]);

  // Subscribe to online users
  useEffect(() => {
    const unsubscribe = ChatService.subscribeToOnlineUsers(setOnlineUsers);
    return unsubscribe;
  }, []);

  // Subscribe to current room messages
  useEffect(() => {
    if (!currentRoom) return;

    const unsubscribe = ChatService.subscribeToRoomMessages(currentRoom.id, setMessages);
    return unsubscribe;
  }, [currentRoom]);

  // Subscribe to typing indicators
  useEffect(() => {
    if (!currentRoom) return;

    const unsubscribe = ChatService.subscribeToTyping(currentRoom.id, (typing) => {
      // Filter out current user's typing
      const otherUsersTyping = typing.filter(t => t.userId !== user?.uid);
      setTypingUsers(otherUsersTyping);
    });

    return unsubscribe;
  }, [currentRoom, user]);

  // Actions
  const sendMessage = useCallback(async (
    content: string,
    type: 'text' | 'image' | 'file' = 'text',
    fileData?: { url: string; fileName: string; fileSize?: number }
  ) => {
    if (!user || !currentRoom || !content.trim()) return;

    try {
      const message = {
        roomId: currentRoom.id,
        senderId: user.uid,
        senderName: user.displayName || user.email || 'Unknown',
        content: content.trim(),
        type,
        ...(fileData && {
          fileUrl: fileData.url,
          fileName: fileData.fileName,
          fileSize: fileData.fileSize
        })
      };

      await ChatService.sendMessage(message);
    } catch (error) {
      console.error('Error sending message:', error);
    }
  }, [user, currentRoom]);

  const createPrivateChat = useCallback(async (userId: string) => {
    if (!user) return;

    try {
      await ChatService.createPrivateRoom(user.uid, userId);
      // Room will be automatically added to rooms list via subscription
    } catch (error) {
      console.error('Error creating private chat:', error);
    }
  }, [user]);

  const addReaction = useCallback(async (messageId: string, emoji: string) => {
    if (!user) return;

    try {
      await ChatService.addReaction(messageId, user.uid, emoji);
    } catch (error) {
      console.error('Error adding reaction:', error);
    }
  }, [user]);

  const removeReaction = useCallback(async (messageId: string, emoji: string) => {
    if (!user) return;

    try {
      await ChatService.removeReaction(messageId, user.uid, emoji);
    } catch (error) {
      console.error('Error removing reaction:', error);
    }
  }, [user]);

  const setTyping = useCallback(async (isTyping: boolean) => {
    if (!user || !currentRoom) return;

    try {
      await ChatService.setTyping(
        currentRoom.id, 
        user.uid, 
        user.displayName || user.email || 'Unknown', 
        isTyping
      );
    } catch (error) {
      console.error('Error setting typing status:', error);
    }
  }, [user, currentRoom]);

  const uploadFile = useCallback(async (file: File) => {
    if (!user || !currentRoom) return;

    try {
      const fileData = await ChatService.uploadFile(file, currentRoom.id);
      
      const fileType = file.type.startsWith('image/') ? 'image' : 'file';
      const content = fileType === 'image' ? '📷 Image' : `📎 ${file.name}`;
      
      await sendMessage(content, fileType, fileData);
    } catch (error) {
      console.error('Error uploading file:', error);
    }
  }, [user, currentRoom, sendMessage]);

  const value: ChatContextType = {
    currentRoom,
    rooms,
    messages,
    onlineUsers,
    typingUsers,
    isLoading,
    setCurrentRoom,
    sendMessage,
    createPrivateChat,
    addReaction,
    removeReaction,
    setTyping,
    uploadFile
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
};
